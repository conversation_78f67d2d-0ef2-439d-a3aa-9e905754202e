"""Base GitHub service with unified API client supporting multiple authentication strategies."""

import asyncio
import httpx
from typing import Dict, Any, Optional, List
from urllib.parse import urljoin

from app.core.logging import get_logger
from app.services.github.auth_strategies import GitHubAuthStrategy

logger = get_logger("github.service")


class GitHubAPIError(Exception):
    """Base exception for GitHub API errors."""
    def __init__(self, message: str, status_code: Optional[int] = None, response_data: Optional[Dict] = None):
        self.message = message
        self.status_code = status_code
        self.response_data = response_data
        super().__init__(message)


class RateLimitExceeded(GitHubAPIError):
    """Raised when GitHub API rate limit is exceeded."""
    def __init__(self, reset_time: str, remaining: int = 0):
        self.reset_time = reset_time
        self.remaining = remaining
        super().__init__(f"Rate limit exceeded. Resets at {reset_time}. Remaining: {remaining}")


class InsufficientPermissions(GitHubAPIError):
    """Raised when GitHub App lacks required permissions."""
    pass


class ResourceNotFound(GitHubAPIError):
    """Raised when requested resource is not found."""
    pass


class GitHubService:
    """Unified GitHub service supporting multiple authentication strategies."""
    
    def __init__(self, auth_strategy: GitHubAuthStrategy, base_url: str = "https://api.github.com"):
        self.auth_strategy = auth_strategy
        self.base_url = base_url
        self.timeout = 30.0
        self.max_retries = 3
        
        # Rate limiting tracking
        self.rate_limit_remaining = 5000
        self.rate_limit_reset = None
        
        logger.info(f"Initialized GitHub service with {auth_strategy.get_auth_method()} authentication")
    
    async def _get_headers(self) -> Dict[str, str]:
        """Get authenticated headers for API requests."""
        return await self.auth_strategy.get_headers()
    
    async def _make_request(
        self, 
        method: str, 
        endpoint: str, 
        params: Optional[Dict[str, Any]] = None,
        json_data: Optional[Dict[str, Any]] = None,
        retry_count: int = 0
    ) -> Dict[str, Any]:
        """Make authenticated request to GitHub API with error handling and retries."""
        headers = await self._get_headers()
        url = urljoin(self.base_url, endpoint.lstrip('/'))
        
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.request(
                    method=method,
                    url=url,
                    headers=headers,
                    params=params,
                    json=json_data
                )
                
                # Update rate limit information
                self._update_rate_limit_info(response.headers)
                
                # Handle different response status codes
                if response.status_code == 200 or response.status_code == 201:
                    return response.json() if response.content else {}
                elif response.status_code == 204:
                    return {}  # No content
                elif response.status_code == 403:
                    await self._handle_403_error(response, method, endpoint, params, json_data, retry_count)
                elif response.status_code == 404:
                    raise ResourceNotFound(f"Resource not found: {endpoint}")
                elif response.status_code == 422:
                    error_data = response.json() if response.content else {}
                    raise GitHubAPIError(f"Validation error: {error_data.get('message', 'Unknown error')}", 422, error_data)
                else:
                    error_data = response.json() if response.content else {}
                    raise GitHubAPIError(
                        f"GitHub API error: {response.status_code} - {error_data.get('message', 'Unknown error')}",
                        response.status_code,
                        error_data
                    )
                    
        except httpx.TimeoutException:
            if retry_count < self.max_retries:
                logger.warning(f"Request timeout, retrying ({retry_count + 1}/{self.max_retries})")
                await asyncio.sleep(2 ** retry_count)  # Exponential backoff
                return await self._make_request(method, endpoint, params, json_data, retry_count + 1)
            raise GitHubAPIError("Request timeout after retries")
        
        except httpx.RequestError as e:
            if retry_count < self.max_retries:
                logger.warning(f"Request error: {e}, retrying ({retry_count + 1}/{self.max_retries})")
                await asyncio.sleep(2 ** retry_count)
                return await self._make_request(method, endpoint, params, json_data, retry_count + 1)
            raise GitHubAPIError(f"Request error: {e}")
    
    async def _handle_403_error(
        self, 
        response: httpx.Response, 
        method: str, 
        endpoint: str, 
        params: Optional[Dict[str, Any]], 
        json_data: Optional[Dict[str, Any]], 
        retry_count: int
    ):
        """Handle 403 Forbidden errors (rate limiting or permissions)."""
        error_data = response.json() if response.content else {}
        error_message = error_data.get('message', '').lower()
        
        if 'rate limit' in error_message or 'api rate limit exceeded' in error_message:
            reset_time = response.headers.get('X-RateLimit-Reset', 'unknown')
            remaining = int(response.headers.get('X-RateLimit-Remaining', 0))
            
            if retry_count < self.max_retries and reset_time != 'unknown':
                # Wait for rate limit reset
                import time
                reset_timestamp = int(reset_time)
                wait_time = max(0, reset_timestamp - int(time.time()) + 1)
                
                if wait_time <= 300:  # Only wait up to 5 minutes
                    logger.warning(f"Rate limit exceeded, waiting {wait_time} seconds")
                    await asyncio.sleep(wait_time)
                    return await self._make_request(method, endpoint, params, json_data, retry_count + 1)
            
            raise RateLimitExceeded(reset_time, remaining)
        else:
            raise InsufficientPermissions(f"Insufficient permissions: {error_data.get('message', 'Unknown error')}")
    
    def _update_rate_limit_info(self, headers: Dict[str, str]):
        """Update rate limit information from response headers."""
        if 'X-RateLimit-Remaining' in headers:
            self.rate_limit_remaining = int(headers['X-RateLimit-Remaining'])
        if 'X-RateLimit-Reset' in headers:
            self.rate_limit_reset = headers['X-RateLimit-Reset']
        
        if self.rate_limit_remaining < 100:
            logger.warning(f"GitHub API rate limit low: {self.rate_limit_remaining} remaining")
    
    async def get(self, endpoint: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Make GET request to GitHub API."""
        return await self._make_request('GET', endpoint, params=params)
    
    async def post(self, endpoint: str, json_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Make POST request to GitHub API."""
        return await self._make_request('POST', endpoint, json_data=json_data)
    
    async def put(self, endpoint: str, json_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Make PUT request to GitHub API."""
        return await self._make_request('PUT', endpoint, json_data=json_data)
    
    async def delete(self, endpoint: str) -> Dict[str, Any]:
        """Make DELETE request to GitHub API."""
        return await self._make_request('DELETE', endpoint)
    
    async def patch(self, endpoint: str, json_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Make PATCH request to GitHub API."""
        return await self._make_request('PATCH', endpoint, json_data=json_data)
    
    def get_rate_limit_info(self) -> Dict[str, Any]:
        """Get current rate limit information."""
        return {
            'remaining': self.rate_limit_remaining,
            'reset_time': self.rate_limit_reset,
            'auth_method': self.auth_strategy.get_auth_method()
        }
    
    async def test_connection(self) -> Dict[str, Any]:
        """Test the GitHub API connection and authentication."""
        try:
            # Test with a simple API call
            response = await self.get('/user')
            return {
                'success': True,
                'auth_method': self.auth_strategy.get_auth_method(),
                'user_info': response,
                'rate_limit': self.get_rate_limit_info()
            }
        except Exception as e:
            return {
                'success': False,
                'auth_method': self.auth_strategy.get_auth_method(),
                'error': str(e),
                'rate_limit': self.get_rate_limit_info()
            }
