"""GitHub team management service."""

from typing import Dict, List, Optional, Any

from app.core.logging import get_logger
from app.services.github.base_service import GitHubService
from app.services.github.service_factory import get_github_service, get_github_org_name

logger = get_logger("github.team")


class GitHubTeamService:
    """Service for managing GitHub organization teams."""
    
    def __init__(self, github_service: Optional[GitHubService] = None):
        self.github_service = github_service or get_github_service()
        self.org_name = get_github_org_name()
    
    async def list_teams(
        self,
        page: int = 1,
        per_page: int = 30,
        search: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        List organization teams.
        
        Args:
            page: Page number for pagination
            per_page: Number of teams per page (max 100)
            search: Search term for team name or description
        """
        params = {
            'page': page,
            'per_page': min(per_page, 100)
        }
        
        endpoint = f"/orgs/{self.org_name}/teams"
        teams = await self.github_service.get(endpoint, params=params)
        
        # Enrich team data and apply search filter
        enriched_teams = []
        for team in teams:
            team_info = {
                'id': team['id'],
                'name': team['name'],
                'slug': team['slug'],
                'description': team.get('description'),
                'privacy': team.get('privacy', 'closed'),
                'members_count': team.get('members_count', 0),
                'repositories_count': team.get('repos_count', 0),
                'created_at': team.get('created_at')
            }
            
            # Apply search filter if provided
            if search:
                search_lower = search.lower()
                if (search_lower not in team_info['name'].lower() and
                    (not team_info.get('description') or search_lower not in team_info['description'].lower())):
                    continue
            
            enriched_teams.append(team_info)
        
        total_count = len(enriched_teams)
        
        return {
            'teams': enriched_teams,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total_count,
                'total_pages': (total_count + per_page - 1) // per_page if total_count > 0 else 1,
                'has_next': page * per_page < total_count,
                'has_prev': page > 1
            }
        }
    
    async def get_team_members(self, team_slug: str) -> Dict[str, Any]:
        """
        Get team members.
        
        Args:
            team_slug: Team slug
        """
        endpoint = f"/orgs/{self.org_name}/teams/{team_slug}/members"
        members = await self.github_service.get(endpoint)
        
        # Enrich member data
        enriched_members = []
        for member in members:
            # Get membership details to determine role
            try:
                membership_endpoint = f"/orgs/{self.org_name}/teams/{team_slug}/memberships/{member['login']}"
                membership = await self.github_service.get(membership_endpoint)
                role = membership.get('role', 'member')
            except Exception:
                role = 'member'  # Default role if membership details unavailable
            
            member_info = {
                'id': member['id'],
                'login': member['login'],
                'avatar_url': member['avatar_url'],
                'html_url': member['html_url'],
                'role': role
            }
            enriched_members.append(member_info)
        
        # Get team details
        team_endpoint = f"/orgs/{self.org_name}/teams/{team_slug}"
        team_data = await self.github_service.get(team_endpoint)
        
        return {
            'team': {
                'id': team_data['id'],
                'name': team_data['name'],
                'slug': team_data['slug'],
                'description': team_data.get('description'),
                'privacy': team_data.get('privacy', 'closed')
            },
            'members': enriched_members
        }
    
    async def get_team_repositories(self, team_slug: str) -> Dict[str, Any]:
        """
        Get repositories accessible to a team.
        
        Args:
            team_slug: Team slug
        """
        endpoint = f"/orgs/{self.org_name}/teams/{team_slug}/repos"
        repositories = await self.github_service.get(endpoint)
        
        # Transform repository data
        repo_data = []
        for repo in repositories:
            repo_info = {
                'id': repo['id'],
                'name': repo['name'],
                'full_name': repo['full_name'],
                'description': repo.get('description'),
                'private': repo['private'],
                'html_url': repo['html_url'],
                'permission': repo.get('permissions', {}).get('admin') and 'admin' or \
                             repo.get('permissions', {}).get('maintain') and 'maintain' or \
                             repo.get('permissions', {}).get('push') and 'push' or 'pull'
            }
            repo_data.append(repo_info)
        
        # Get team details
        team_endpoint = f"/orgs/{self.org_name}/teams/{team_slug}"
        team_data = await self.github_service.get(team_endpoint)
        
        return {
            'team': {
                'id': team_data['id'],
                'name': team_data['name'],
                'slug': team_data['slug'],
                'description': team_data.get('description')
            },
            'repositories': repo_data
        }
    
    async def get_team_details(self, team_slug: str) -> Dict[str, Any]:
        """
        Get detailed team information including members and repositories.
        
        Args:
            team_slug: Team slug
        """
        # Get team info
        team_endpoint = f"/orgs/{self.org_name}/teams/{team_slug}"
        team_data = await self.github_service.get(team_endpoint)
        
        # Get members and repositories
        members_data = await self.get_team_members(team_slug)
        repos_data = await self.get_team_repositories(team_slug)
        
        return {
            'id': team_data['id'],
            'name': team_data['name'],
            'slug': team_data['slug'],
            'description': team_data.get('description'),
            'privacy': team_data.get('privacy', 'closed'),
            'members_count': len(members_data['members']),
            'repositories_count': len(repos_data['repositories']),
            'members': members_data['members'],
            'repositories': repos_data['repositories'],
            'created_at': team_data.get('created_at'),
            'updated_at': team_data.get('updated_at')
        }
    
    async def create_team(
        self,
        name: str,
        description: Optional[str] = None,
        privacy: str = "closed"
    ) -> Dict[str, Any]:
        """
        Create a new team.
        
        Args:
            name: Team name
            description: Team description
            privacy: Team privacy (closed, secret)
        """
        endpoint = f"/orgs/{self.org_name}/teams"
        data = {
            'name': name,
            'privacy': privacy
        }
        
        if description:
            data['description'] = description
        
        team_data = await self.github_service.post(endpoint, json_data=data)
        
        return {
            'id': team_data['id'],
            'name': team_data['name'],
            'slug': team_data['slug'],
            'description': team_data.get('description'),
            'privacy': team_data.get('privacy'),
            'created_at': team_data.get('created_at')
        }
    
    async def update_team(
        self,
        team_slug: str,
        name: Optional[str] = None,
        description: Optional[str] = None,
        privacy: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Update team information.
        
        Args:
            team_slug: Team slug
            name: New team name
            description: New team description
            privacy: New team privacy setting
        """
        endpoint = f"/orgs/{self.org_name}/teams/{team_slug}"
        data = {}
        
        if name:
            data['name'] = name
        if description is not None:
            data['description'] = description
        if privacy:
            data['privacy'] = privacy
        
        team_data = await self.github_service.patch(endpoint, json_data=data)
        
        return {
            'id': team_data['id'],
            'name': team_data['name'],
            'slug': team_data['slug'],
            'description': team_data.get('description'),
            'privacy': team_data.get('privacy'),
            'updated_at': team_data.get('updated_at')
        }
    
    async def delete_team(self, team_slug: str) -> Dict[str, Any]:
        """
        Delete a team.
        
        Args:
            team_slug: Team slug
        """
        endpoint = f"/orgs/{self.org_name}/teams/{team_slug}"
        
        await self.github_service.delete(endpoint)
        
        return {
            'team_slug': team_slug,
            'status': 'deleted'
        }
