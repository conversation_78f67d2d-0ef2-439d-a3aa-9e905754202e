"""GitHub repository management service."""

from typing import Dict, List, Optional, Any
from urllib.parse import quote

from app.core.logging import get_logger
from app.services.github.base_service import GitHubService
from app.services.github.service_factory import get_github_service, get_github_org_name

logger = get_logger("github.repository")


class GitHubRepositoryService:
    """Service for managing GitHub repositories and team access."""
    
    def __init__(self, github_service: Optional[GitHubService] = None):
        self.github_service = github_service or get_github_service()
        self.org_name = get_github_org_name()
    
    async def list_repositories(
        self,
        page: int = 1,
        per_page: int = 30,
        search: Optional[str] = None,
        visibility: str = "all",
        sort: str = "updated",
        order: str = "desc"
    ) -> Dict[str, Any]:
        """
        List organization repositories.
        
        Args:
            page: Page number for pagination
            per_page: Number of repositories per page (max 100)
            search: Search term for repository name/description
            visibility: Filter by visibility (public, private, all)
            sort: Sort by (name, updated, created, pushed, full_name)
            order: Sort order (asc, desc)
        """
        params = {
            'page': page,
            'per_page': min(per_page, 100),
            'sort': sort,
            'direction': order
        }
        
        if visibility != "all":
            params['type'] = visibility
        
        if search:
            # Use search API for filtering
            params['q'] = f"{search} org:{self.org_name}"
            endpoint = "/search/repositories"
            response = await self.github_service.get(endpoint, params=params)
            
            # Transform search response to match org repos format
            repositories = response.get('items', [])
            total_count = response.get('total_count', 0)
        else:
            endpoint = f"/orgs/{self.org_name}/repos"
            repositories = await self.github_service.get(endpoint, params=params)
            total_count = len(repositories)  # GitHub doesn't provide total count for org repos
        
        # Enrich repositories with team count
        enriched_repos = []
        for repo in repositories:
            repo_data = {
                'id': repo['id'],
                'name': repo['name'],
                'full_name': repo['full_name'],
                'description': repo.get('description'),
                'private': repo['private'],
                'html_url': repo['html_url'],
                'clone_url': repo['clone_url'],
                'ssh_url': repo['ssh_url'],
                'language': repo.get('language'),
                'default_branch': repo.get('default_branch', 'main'),
                'updated_at': repo['updated_at'],
                'created_at': repo['created_at'],
                'teams_count': 0  # Will be populated separately if needed
            }
            enriched_repos.append(repo_data)
        
        return {
            'repositories': enriched_repos,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total_count,
                'total_pages': (total_count + per_page - 1) // per_page if total_count > 0 else 1,
                'has_next': page * per_page < total_count,
                'has_prev': page > 1
            }
        }
    
    async def get_repository_teams(self, repo_name: str) -> Dict[str, Any]:
        """
        Get teams with access to a specific repository.
        
        Args:
            repo_name: Repository name
        """
        endpoint = f"/repos/{self.org_name}/{repo_name}/teams"
        teams = await self.github_service.get(endpoint)
        
        # Transform team data
        team_data = []
        for team in teams:
            team_info = {
                'id': team['id'],
                'name': team['name'],
                'slug': team['slug'],
                'description': team.get('description'),
                'permission': team.get('permission', 'pull'),
                'members_count': team.get('members_count', 0)
            }
            team_data.append(team_info)
        
        return {
            'repository': {
                'name': repo_name,
                'full_name': f"{self.org_name}/{repo_name}"
            },
            'teams': team_data
        }
    
    async def add_team_to_repository(
        self,
        repo_name: str,
        team_slug: str,
        permission: str = "pull"
    ) -> Dict[str, Any]:
        """
        Add team to repository with specific permission.
        
        Args:
            repo_name: Repository name
            team_slug: Team slug
            permission: Permission level (pull, push, maintain, admin)
        """
        endpoint = f"/orgs/{self.org_name}/teams/{team_slug}/repos/{self.org_name}/{repo_name}"
        data = {'permission': permission}
        
        await self.github_service.put(endpoint, json_data=data)
        
        return {
            'repository_name': repo_name,
            'team_slug': team_slug,
            'permission': permission,
            'status': 'success'
        }
    
    async def remove_team_from_repository(
        self,
        repo_name: str,
        team_slug: str
    ) -> Dict[str, Any]:
        """
        Remove team from repository.
        
        Args:
            repo_name: Repository name
            team_slug: Team slug
        """
        endpoint = f"/orgs/{self.org_name}/teams/{team_slug}/repos/{self.org_name}/{repo_name}"
        
        await self.github_service.delete(endpoint)
        
        return {
            'repository_name': repo_name,
            'team_slug': team_slug,
            'status': 'removed'
        }
    
    async def bulk_assign_teams_to_repositories(
        self,
        repository_names: List[str],
        team_assignments: List[Dict[str, str]],
        replace_existing: bool = False
    ) -> Dict[str, Any]:
        """
        Bulk assign teams to multiple repositories.
        
        Args:
            repository_names: List of repository names
            team_assignments: List of team assignments with team_slug and permission
            replace_existing: Whether to replace existing team assignments
        """
        results = []
        assignments_created = 0
        assignments_failed = 0
        
        for repo_name in repository_names:
            if replace_existing:
                try:
                    # Remove existing teams first
                    current_teams_data = await self.get_repository_teams(repo_name)
                    for team in current_teams_data['teams']:
                        await self.remove_team_from_repository(repo_name, team['slug'])
                except Exception as e:
                    logger.warning(f"Failed to remove existing teams from {repo_name}: {e}")
            
            # Add new team assignments
            for assignment in team_assignments:
                try:
                    await self.add_team_to_repository(
                        repo_name,
                        assignment['team_slug'],
                        assignment['permission']
                    )
                    results.append({
                        'repository_name': repo_name,
                        'team_slug': assignment['team_slug'],
                        'permission': assignment['permission'],
                        'status': 'success'
                    })
                    assignments_created += 1
                except Exception as e:
                    logger.error(f"Failed to assign team {assignment['team_slug']} to {repo_name}: {e}")
                    results.append({
                        'repository_name': repo_name,
                        'team_slug': assignment['team_slug'],
                        'permission': assignment.get('permission', 'unknown'),
                        'status': 'failed',
                        'error': str(e)
                    })
                    assignments_failed += 1
        
        return {
            'assignments_created': assignments_created,
            'assignments_failed': assignments_failed,
            'results': results
        }
    
    async def get_repository_details(self, repo_name: str) -> Dict[str, Any]:
        """
        Get detailed repository information including team access.
        
        Args:
            repo_name: Repository name
        """
        # Get repository info
        repo_endpoint = f"/repos/{self.org_name}/{repo_name}"
        repo_data = await self.github_service.get(repo_endpoint)
        
        # Get team access
        teams_data = await self.get_repository_teams(repo_name)
        
        return {
            'id': repo_data['id'],
            'name': repo_data['name'],
            'full_name': repo_data['full_name'],
            'description': repo_data.get('description'),
            'private': repo_data['private'],
            'html_url': repo_data['html_url'],
            'clone_url': repo_data['clone_url'],
            'ssh_url': repo_data['ssh_url'],
            'language': repo_data.get('language'),
            'default_branch': repo_data.get('default_branch', 'main'),
            'teams': teams_data['teams'],
            'created_at': repo_data['created_at'],
            'updated_at': repo_data['updated_at']
        }
