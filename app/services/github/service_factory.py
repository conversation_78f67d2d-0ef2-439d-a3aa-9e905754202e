"""GitHub service factory for creating GitHub services with proper configuration."""

import os
from typing import Optional

from app.core.config import get_settings
from app.core.logging import get_logger
from app.services.github.auth_strategies import (
    GitHubAuthStrategy,
    GitHubAppAuthStrategy,
    PersonalAccessTokenAuthStrategy,
    GitHubAuthError,
    GitHubAppConfigError,
    PersonalAccessTokenError,
    validate_github_config
)
from app.services.github.base_service import GitHubService

logger = get_logger("github.factory")


class GitHubServiceFactory:
    """Factory for creating GitHub services with proper configuration."""
    
    _instance: Optional[GitHubService] = None
    
    @classmethod
    def create_github_service(cls, force_recreate: bool = False) -> GitHubService:
        """Create or return cached GitHub service instance."""
        if cls._instance is None or force_recreate:
            cls._instance = cls._create_new_service()
        return cls._instance
    
    @classmethod
    def _create_new_service(cls) -> GitHubService:
        """Create a new GitHub service instance."""
        settings = get_settings()
        
        # Validate configuration
        cls._validate_configuration()
        
        # Create authentication strategy
        auth_strategy = cls._create_auth_strategy(settings)
        
        # Create and return service
        service = GitHubService(
            auth_strategy=auth_strategy,
            base_url=settings.github.api_base_url
        )
        
        logger.info(f"Created GitHub service with {auth_strategy.get_auth_method()} authentication")
        return service
    
    @classmethod
    def _validate_configuration(cls) -> None:
        """Validate GitHub configuration."""
        try:
            validate_github_config()
        except Exception as e:
            logger.error(f"GitHub configuration validation failed: {e}")
            raise
    
    @classmethod
    def _create_auth_strategy(cls, settings) -> GitHubAuthStrategy:
        """Create authentication strategy based on configuration."""
        auth_method = settings.github.auth_method.lower()
        
        if auth_method == 'github_app':
            return cls._create_github_app_strategy(settings)
        elif auth_method == 'personal_access_token':
            return cls._create_pat_strategy(settings)
        else:
            raise GitHubAuthError(f"Unsupported authentication method: {auth_method}")
    
    @classmethod
    def _create_github_app_strategy(cls, settings) -> GitHubAppAuthStrategy:
        """Create GitHub App authentication strategy."""
        app_id = settings.github.app_id or os.getenv('GITHUB_APP_ID')
        installation_id = settings.github.installation_id or os.getenv('GITHUB_APP_INSTALLATION_ID')
        private_key_path = settings.github.private_key_path or os.getenv('GITHUB_APP_PRIVATE_KEY_PATH')
        private_key_content = settings.github.private_key or os.getenv('GITHUB_APP_PRIVATE_KEY')
        
        if not app_id or not installation_id:
            raise GitHubAppConfigError(
                "GitHub App ID and Installation ID are required for GitHub App authentication"
            )
        
        # Get private key from file or environment variable
        if private_key_path and os.path.exists(private_key_path):
            with open(private_key_path, 'r') as f:
                private_key = f.read()
        elif private_key_content:
            private_key = private_key_content
        else:
            raise GitHubAppConfigError(
                "Either private_key_path or private_key is required for GitHub App authentication"
            )
        
        return GitHubAppAuthStrategy(app_id, private_key, installation_id)
    
    @classmethod
    def _create_pat_strategy(cls, settings) -> PersonalAccessTokenAuthStrategy:
        """Create Personal Access Token authentication strategy."""
        token = settings.github.personal_access_token or os.getenv('GITHUB_PERSONAL_ACCESS_TOKEN')
        
        if not token:
            raise PersonalAccessTokenError(
                "Personal Access Token is required for PAT authentication"
            )
        
        return PersonalAccessTokenAuthStrategy(token)
    
    @classmethod
    def get_github_org_name(cls) -> str:
        """Get the configured GitHub organization name."""
        settings = get_settings()
        org_name = settings.github.org_name
        
        if not org_name:
            raise GitHubAuthError("GitHub organization name is required")
        
        return org_name
    
    @classmethod
    async def test_github_connection(cls) -> dict:
        """Test GitHub connection and return status."""
        try:
            service = cls.create_github_service()
            result = await service.test_connection()
            
            # Add organization info
            if result['success']:
                org_name = cls.get_github_org_name()
                result['organization'] = org_name
            
            return result
        except Exception as e:
            logger.error(f"GitHub connection test failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'auth_method': 'unknown'
            }


# Convenience functions for easy access
def get_github_service() -> GitHubService:
    """Get GitHub service instance."""
    return GitHubServiceFactory.create_github_service()


def get_github_org_name() -> str:
    """Get GitHub organization name."""
    return GitHubServiceFactory.get_github_org_name()


async def test_github_connection() -> dict:
    """Test GitHub connection."""
    return await GitHubServiceFactory.test_github_connection()
