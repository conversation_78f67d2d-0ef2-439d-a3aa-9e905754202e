"""GitHub user management service."""

from typing import Dict, List, Optional, Any

from app.core.logging import get_logger
from app.services.github.base_service import GitHubService
from app.services.github.service_factory import get_github_service, get_github_org_name

logger = get_logger("github.user")


class GitHubUserService:
    """Service for managing GitHub organization users and team memberships."""

    def __init__(self, github_service: Optional[GitHubService] = None):
        self.github_service = github_service or get_github_service()
        self.org_name = get_github_org_name()

    async def list_organization_members(
        self,
        page: int = 1,
        per_page: int = 30,
        role: str = "all",
        search: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        List organization members.

        Args:
            page: Page number for pagination
            per_page: Number of users per page (max 100)
            role: Filter by role (all, admin, member)
            search: Search term for username
        """
        params = {
            'page': page,
            'per_page': min(per_page, 100)
        }

        if role != "all":
            params['role'] = role

        endpoint = f"/orgs/{self.org_name}/members"
        members = await self.github_service.get(endpoint, params=params)

        # Process member data without additional API calls
        processed_members = []
        for member in members:
            member_info = {
                'id': member['id'],
                'login': member['login'],
                'email': None,  # Not available in org members endpoint
                'name': None,   # Not available in org members endpoint
                'avatar_url': member['avatar_url'],
                'html_url': member['html_url'],
                'role': 'member',  # Default role from org members endpoint
                'teams_count': 0,  # Will be populated separately if needed
                'created_at': None  # Not available in org members endpoint
            }

            # Apply search filter if provided (only on username since that's what we have)
            if search:
                search_lower = search.lower()
                if search_lower not in member_info['login'].lower():
                    continue

            processed_members.append(member_info)

        total_count = len(processed_members)

        return {
            'users': processed_members,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total_count,
                'total_pages': (total_count + per_page - 1) // per_page if total_count > 0 else 1,
                'has_next': page * per_page < total_count,
                'has_prev': page > 1
            }
        }

    async def _get_user_details(self, username: str) -> Dict[str, Any]:
        """Get detailed user information."""
        try:
            endpoint = f"/users/{username}"
            return await self.github_service.get(endpoint)
        except Exception as e:
            logger.warning(f"Failed to get user details for {username}: {e}")
            return {}

    async def get_user_teams(self, username: str) -> Dict[str, Any]:
        """
        Get user's team memberships in the organization.

        Args:
            username: GitHub username
        """
        # Get all organization teams
        teams_endpoint = f"/orgs/{self.org_name}/teams"
        all_teams = await self.github_service.get(teams_endpoint)

        user_teams = []
        for team in all_teams:
            try:
                # Check if user is member of this team
                membership_endpoint = f"/orgs/{self.org_name}/teams/{team['slug']}/memberships/{username}"
                membership = await self.github_service.get(membership_endpoint)

                if membership.get('state') == 'active':
                    team_info = {
                        'id': team['id'],
                        'name': team['name'],
                        'slug': team['slug'],
                        'description': team.get('description'),
                        'repositories_count': team.get('repos_count', 0)
                    }
                    user_teams.append(team_info)
            except Exception:
                # User is not a member of this team, continue
                continue

        # Get user details
        user_details = await self._get_user_details(username)

        return {
            'user': {
                'login': username,
                'email': user_details.get('email'),
                'name': user_details.get('name'),
                'avatar_url': user_details.get('avatar_url'),
                'role': 'member'  # Default role
            },
            'teams': user_teams
        }

    async def add_user_to_team(
        self,
        username: str,
        team_slug: str,
        role: str = "member"
    ) -> Dict[str, Any]:
        """
        Add user to team.

        Args:
            username: GitHub username
            team_slug: Team slug
            role: Team role (member, maintainer)
        """
        endpoint = f"/orgs/{self.org_name}/teams/{team_slug}/memberships/{username}"
        data = {'role': role}

        response = await self.github_service.put(endpoint, json_data=data)

        return {
            'username': username,
            'team_slug': team_slug,
            'role': role,
            'state': response.get('state', 'active'),
            'status': 'success'
        }

    async def remove_user_from_team(
        self,
        username: str,
        team_slug: str
    ) -> Dict[str, Any]:
        """
        Remove user from team.

        Args:
            username: GitHub username
            team_slug: Team slug
        """
        endpoint = f"/orgs/{self.org_name}/teams/{team_slug}/memberships/{username}"

        await self.github_service.delete(endpoint)

        return {
            'username': username,
            'team_slug': team_slug,
            'status': 'removed'
        }

    async def bulk_assign_users_to_teams(
        self,
        usernames: List[str],
        team_slugs: List[str],
        role: str = "member",
        replace_existing: bool = False
    ) -> Dict[str, Any]:
        """
        Bulk assign users to multiple teams.

        Args:
            usernames: List of GitHub usernames
            team_slugs: List of team slugs
            role: Team role for all assignments (member, maintainer)
            replace_existing: Whether to replace existing team memberships
        """
        results = []
        assignments_created = 0
        assignments_failed = 0

        for username in usernames:
            if replace_existing:
                try:
                    # Remove user from existing teams first
                    current_teams_data = await self.get_user_teams(username)
                    for team in current_teams_data['teams']:
                        await self.remove_user_from_team(username, team['slug'])
                except Exception as e:
                    logger.warning(f"Failed to remove existing teams for {username}: {e}")

            # Add user to new teams
            for team_slug in team_slugs:
                try:
                    await self.add_user_to_team(username, team_slug, role)
                    results.append({
                        'username': username,
                        'team_slug': team_slug,
                        'role': role,
                        'status': 'success'
                    })
                    assignments_created += 1
                except Exception as e:
                    logger.error(f"Failed to assign user {username} to team {team_slug}: {e}")
                    results.append({
                        'username': username,
                        'team_slug': team_slug,
                        'role': role,
                        'status': 'failed',
                        'error': str(e)
                    })
                    assignments_failed += 1

        return {
            'assignments_created': assignments_created,
            'assignments_failed': assignments_failed,
            'results': results
        }

    async def get_user_details(self, username: str) -> Dict[str, Any]:
        """
        Get detailed user information including team memberships.
        This method makes additional API calls to get complete user information.

        Args:
            username: GitHub username
        """
        # Get user info and teams
        user_teams_data = await self.get_user_teams(username)
        user_details = await self._get_user_details(username)

        return {
            'id': user_details.get('id'),
            'login': username,
            'email': user_details.get('email'),
            'name': user_details.get('name'),
            'avatar_url': user_details.get('avatar_url'),
            'html_url': user_details.get('html_url'),
            'role': 'member',  # Organization role
            'teams': user_teams_data['teams'],
            'created_at': user_details.get('created_at')
        }

    async def get_user_with_details(self, username: str) -> Dict[str, Any]:
        """
        Get user information with full details (makes additional API call).
        Use this when you need complete user information including email, name, etc.

        Args:
            username: GitHub username
        """
        user_details = await self._get_user_details(username)

        return {
            'id': user_details.get('id'),
            'login': username,
            'email': user_details.get('email'),
            'name': user_details.get('name'),
            'avatar_url': user_details.get('avatar_url'),
            'html_url': user_details.get('html_url'),
            'created_at': user_details.get('created_at'),
            'bio': user_details.get('bio'),
            'company': user_details.get('company'),
            'location': user_details.get('location'),
            'public_repos': user_details.get('public_repos'),
            'followers': user_details.get('followers'),
            'following': user_details.get('following')
        }
