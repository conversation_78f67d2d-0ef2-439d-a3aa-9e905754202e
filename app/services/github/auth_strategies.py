"""GitHub authentication strategies for different authentication methods."""

import os
import time
import jwt
import httpx
from abc import ABC, abstractmethod
from typing import Dict, Optional
from datetime import datetime, timedelta

from app.core.logging import get_logger

logger = get_logger("github.auth")


class GitHubAuthStrategy(ABC):
    """Abstract base class for GitHub authentication strategies."""
    
    @abstractmethod
    async def get_headers(self) -> Dict[str, str]:
        """Get authenticated headers for API requests."""
        pass
    
    @abstractmethod
    def get_auth_method(self) -> str:
        """Get the authentication method name."""
        pass


class GitHubAppAuthStrategy(GitHubAuthStrategy):
    """GitHub App authentication strategy using JWT and installation tokens."""
    
    def __init__(self, app_id: str, private_key: str, installation_id: str):
        self.app_id = app_id
        self.private_key = private_key
        self.installation_id = installation_id
        self._cached_token: Optional[str] = None
        self._token_expires_at: Optional[datetime] = None
    
    def get_auth_method(self) -> str:
        return "github_app"
    
    async def get_headers(self) -> Dict[str, str]:
        """Get authenticated headers using GitHub App installation token."""
        token = await self._get_installation_token()
        return {
            'Authorization': f'token {token}',
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'TaskMaster-GitHub-Integration'
        }
    
    def _generate_jwt_token(self) -> str:
        """Generate JWT token for GitHub App authentication."""
        now = int(time.time())
        payload = {
            'iat': now,
            'exp': now + (10 * 60),  # 10 minutes
            'iss': self.app_id
        }
        return jwt.encode(payload, self.private_key, algorithm='RS256')
    
    async def _get_installation_token(self) -> str:
        """Get installation access token with caching."""
        # Check if cached token is still valid
        if (self._cached_token and self._token_expires_at and 
            datetime.utcnow() < self._token_expires_at - timedelta(minutes=5)):
            return self._cached_token
        
        # Generate new token
        jwt_token = self._generate_jwt_token()
        headers = {
            'Authorization': f'Bearer {jwt_token}',
            'Accept': 'application/vnd.github.v3+json'
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f'https://api.github.com/app/installations/{self.installation_id}/access_tokens',
                headers=headers,
                timeout=30.0
            )
            response.raise_for_status()
            
            token_data = response.json()
            self._cached_token = token_data['token']
            
            # Parse expiration time
            expires_at_str = token_data['expires_at']
            self._token_expires_at = datetime.fromisoformat(
                expires_at_str.replace('Z', '+00:00')
            ).replace(tzinfo=None)
            
            logger.info(f"Generated new GitHub App installation token, expires at {self._token_expires_at}")
            return self._cached_token


class PersonalAccessTokenAuthStrategy(GitHubAuthStrategy):
    """Personal Access Token authentication strategy."""
    
    def __init__(self, personal_access_token: str):
        self.personal_access_token = personal_access_token
    
    def get_auth_method(self) -> str:
        return "personal_access_token"
    
    async def get_headers(self) -> Dict[str, str]:
        """Get authenticated headers using Personal Access Token."""
        return {
            'Authorization': f'token {self.personal_access_token}',
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'TaskMaster-GitHub-Integration'
        }


class GitHubAuthError(Exception):
    """Base exception for GitHub authentication errors."""
    pass


class GitHubAppConfigError(GitHubAuthError):
    """Raised when GitHub App configuration is invalid."""
    pass


class PersonalAccessTokenError(GitHubAuthError):
    """Raised when Personal Access Token is invalid."""
    pass


def create_github_auth_strategy() -> GitHubAuthStrategy:
    """Factory function to create GitHub authentication strategy based on configuration."""
    auth_method = os.getenv('GITHUB_AUTH_METHOD', 'github_app').lower()
    
    if auth_method == 'github_app':
        # GitHub App Authentication
        app_id = os.getenv('GITHUB_APP_ID')
        private_key_path = os.getenv('GITHUB_APP_PRIVATE_KEY_PATH')
        private_key_content = os.getenv('GITHUB_APP_PRIVATE_KEY')
        installation_id = os.getenv('GITHUB_APP_INSTALLATION_ID')
        
        if not app_id or not installation_id:
            raise GitHubAppConfigError(
                "GITHUB_APP_ID and GITHUB_APP_INSTALLATION_ID are required for GitHub App authentication"
            )
        
        # Get private key from file or environment variable
        if private_key_path and os.path.exists(private_key_path):
            with open(private_key_path, 'r') as f:
                private_key = f.read()
        elif private_key_content:
            private_key = private_key_content
        else:
            raise GitHubAppConfigError(
                "Either GITHUB_APP_PRIVATE_KEY_PATH or GITHUB_APP_PRIVATE_KEY is required"
            )
        
        logger.info(f"Using GitHub App authentication with App ID: {app_id}")
        return GitHubAppAuthStrategy(app_id, private_key, installation_id)
    
    elif auth_method == 'personal_access_token':
        # Personal Access Token Authentication
        token = os.getenv('GITHUB_PERSONAL_ACCESS_TOKEN')
        if not token:
            raise PersonalAccessTokenError(
                "GITHUB_PERSONAL_ACCESS_TOKEN is required for PAT authentication"
            )
        
        logger.info("Using Personal Access Token authentication")
        return PersonalAccessTokenAuthStrategy(token)
    
    else:
        raise GitHubAuthError(f"Unsupported authentication method: {auth_method}")


def validate_github_config() -> None:
    """Validate GitHub configuration based on authentication method."""
    auth_method = os.getenv('GITHUB_AUTH_METHOD', 'github_app').lower()
    
    if auth_method == 'github_app':
        required_vars = ['GITHUB_APP_ID', 'GITHUB_APP_INSTALLATION_ID']
        missing_vars = [var for var in required_vars if not os.getenv(var)]
        
        # Check for private key
        private_key_path = os.getenv('GITHUB_APP_PRIVATE_KEY_PATH')
        private_key_content = os.getenv('GITHUB_APP_PRIVATE_KEY')
        
        if not private_key_path and not private_key_content:
            missing_vars.append('GITHUB_APP_PRIVATE_KEY_PATH or GITHUB_APP_PRIVATE_KEY')
        elif private_key_path and not os.path.exists(private_key_path):
            raise GitHubAppConfigError(f"Private key file not found: {private_key_path}")
        
        if missing_vars:
            raise GitHubAppConfigError(f"Missing required environment variables: {missing_vars}")
    
    elif auth_method == 'personal_access_token':
        if not os.getenv('GITHUB_PERSONAL_ACCESS_TOKEN'):
            raise PersonalAccessTokenError("GITHUB_PERSONAL_ACCESS_TOKEN is required")
    
    else:
        raise GitHubAuthError(f"Invalid GITHUB_AUTH_METHOD: {auth_method}")
    
    logger.info(f"GitHub configuration validated for method: {auth_method}")
