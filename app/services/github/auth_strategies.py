"""GitHub authentication strategies for different authentication methods."""

import os
import time
import jwt
import httpx
from abc import ABC, abstractmethod
from typing import Dict, Optional
from datetime import datetime, timedelta

from app.core.logging import get_logger

logger = get_logger("github.auth")


class GitHubAuthStrategy(ABC):
    """Abstract base class for GitHub authentication strategies."""

    @abstractmethod
    async def get_headers(self) -> Dict[str, str]:
        """Get authenticated headers for API requests."""
        pass

    @abstractmethod
    def get_auth_method(self) -> str:
        """Get the authentication method name."""
        pass


class GitHubAppAuthStrategy(GitHubAuthStrategy):
    """GitHub App authentication strategy using JWT and installation tokens."""

    def __init__(self, app_id: str, private_key: str, installation_id: str):
        self.app_id = app_id
        self.private_key = private_key
        self.installation_id = installation_id
        self._cached_token: Optional[str] = None
        self._token_expires_at: Optional[datetime] = None

    def get_auth_method(self) -> str:
        return "github_app"

    async def get_headers(self) -> Dict[str, str]:
        """Get authenticated headers using GitHub App installation token."""
        token = await self._get_installation_token()
        return {
            'Authorization': f'token {token}',
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'TaskMaster-GitHub-Integration'
        }

    def _generate_jwt_token(self) -> str:
        """Generate JWT token for GitHub App authentication."""
        now = int(time.time())
        payload = {
            'iat': now,
            'exp': now + (10 * 60),  # 10 minutes
            'iss': self.app_id
        }
        return jwt.encode(payload, self.private_key, algorithm='RS256')

    async def _get_installation_token(self) -> str:
        """Get installation access token with caching."""
        # Check if cached token is still valid
        if (self._cached_token and self._token_expires_at and
            datetime.utcnow() < self._token_expires_at - timedelta(minutes=5)):
            return self._cached_token

        # Generate new token
        jwt_token = self._generate_jwt_token()
        headers = {
            'Authorization': f'Bearer {jwt_token}',
            'Accept': 'application/vnd.github.v3+json'
        }

        async with httpx.AsyncClient() as client:
            response = await client.post(
                f'https://api.github.com/app/installations/{self.installation_id}/access_tokens',
                headers=headers,
                timeout=30.0
            )
            response.raise_for_status()

            token_data = response.json()
            self._cached_token = token_data['token']

            # Parse expiration time
            expires_at_str = token_data['expires_at']
            self._token_expires_at = datetime.fromisoformat(
                expires_at_str.replace('Z', '+00:00')
            ).replace(tzinfo=None)

            logger.info(f"Generated new GitHub App installation token, expires at {self._token_expires_at}")
            return self._cached_token


class PersonalAccessTokenAuthStrategy(GitHubAuthStrategy):
    """Personal Access Token authentication strategy."""

    def __init__(self, personal_access_token: str):
        self.personal_access_token = personal_access_token

    def get_auth_method(self) -> str:
        return "personal_access_token"

    async def get_headers(self) -> Dict[str, str]:
        """Get authenticated headers using Personal Access Token."""
        return {
            'Authorization': f'token {self.personal_access_token}',
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'TaskMaster-GitHub-Integration'
        }


class GitHubAuthError(Exception):
    """Base exception for GitHub authentication errors."""
    pass


class GitHubAppConfigError(GitHubAuthError):
    """Raised when GitHub App configuration is invalid."""
    pass


class PersonalAccessTokenError(GitHubAuthError):
    """Raised when Personal Access Token is invalid."""
    pass


def validate_github_config() -> None:
    """Validate GitHub configuration based on authentication method."""
    from app.core.config import get_settings

    try:
        settings = get_settings()
        auth_method = settings.github.auth_method.lower()

        # Common validation
        if not settings.github.org_name:
            raise GitHubAuthError("GitHub organization name is required")

        if auth_method == 'github_app':
            if not settings.github.app_id:
                raise GitHubAppConfigError("GitHub App ID is required")
            if not settings.github.installation_id:
                raise GitHubAppConfigError("GitHub App Installation ID is required")

            # Check for private key
            if not settings.github.private_key_path and not settings.github.private_key:
                raise GitHubAppConfigError("Either private_key_path or private_key is required")
            elif settings.github.private_key_path and not os.path.exists(settings.github.private_key_path):
                raise GitHubAppConfigError(f"Private key file not found: {settings.github.private_key_path}")

        elif auth_method == 'personal_access_token':
            if not settings.github.personal_access_token:
                raise PersonalAccessTokenError("Personal Access Token is required")

        else:
            raise GitHubAuthError(f"Invalid authentication method: {auth_method}")

        logger.info(f"GitHub configuration validated for method: {auth_method}")

    except Exception as e:
        logger.error(f"GitHub configuration validation failed: {e}")
        raise
