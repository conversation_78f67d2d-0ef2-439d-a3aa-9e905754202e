"""GitHub API request/response schemas."""

from typing import List, Optional
from pydantic import BaseModel, Field


class TeamAssignment(BaseModel):
    """Team assignment for repository or user operations."""
    team_slug: str = Field(..., description="GitHub team slug")
    permission: str = Field(..., description="Permission level (pull, push, maintain, admin)")


class BulkRepositoryTeamAssignRequest(BaseModel):
    """Request schema for bulk repository team assignment."""
    repository_names: List[str] = Field(..., description="List of repository names")
    team_assignments: List[TeamAssignment] = Field(..., description="List of team assignments")
    replace_existing: bool = Field(False, description="Whether to replace existing team assignments")


class BulkUserTeamAssignRequest(BaseModel):
    """Request schema for bulk user team assignment."""
    usernames: List[str] = Field(..., description="List of GitHub usernames")
    team_slugs: List[str] = Field(..., description="List of team slugs")
    role: str = Field("member", description="Team role (member, maintainer)")
    replace_existing: bool = Field(False, description="Whether to replace existing team memberships")


class AddUserToTeamRequest(BaseModel):
    """Request schema for adding user to team."""
    role: str = Field("member", description="Team role (member, maintainer)")


class AddTeamToRepositoryRequest(BaseModel):
    """Request schema for adding team to repository."""
    permission: str = Field(..., description="Permission level (pull, push, maintain, admin)")


class AssignmentResult(BaseModel):
    """Result of a single assignment operation."""
    status: str = Field(..., description="Status of the operation (success, failed)")
    error: Optional[str] = Field(None, description="Error message if operation failed")


class RepositoryTeamAssignmentResult(AssignmentResult):
    """Result of repository team assignment."""
    repository_name: str = Field(..., description="Repository name")
    team_slug: str = Field(..., description="Team slug")
    permission: str = Field(..., description="Permission level")


class UserTeamAssignmentResult(AssignmentResult):
    """Result of user team assignment."""
    username: str = Field(..., description="GitHub username")
    team_slug: str = Field(..., description="Team slug")
    role: str = Field(..., description="Team role")


class BulkRepositoryTeamAssignResponse(BaseModel):
    """Response schema for bulk repository team assignment."""
    assignments_created: int = Field(..., description="Number of successful assignments")
    assignments_failed: int = Field(..., description="Number of failed assignments")
    results: List[RepositoryTeamAssignmentResult] = Field(..., description="Detailed results")


class BulkUserTeamAssignResponse(BaseModel):
    """Response schema for bulk user team assignment."""
    assignments_created: int = Field(..., description="Number of successful assignments")
    assignments_failed: int = Field(..., description="Number of failed assignments")
    results: List[UserTeamAssignmentResult] = Field(..., description="Detailed results")


class TeamMembershipResponse(BaseModel):
    """Response schema for team membership operations."""
    url: str = Field(..., description="GitHub API URL for the membership")
    role: str = Field(..., description="Team role")
    state: str = Field(..., description="Membership state (active, pending)")


class RepositoryTeamPermissionResponse(BaseModel):
    """Response schema for repository team permission operations."""
    repository_name: str = Field(..., description="Repository name")
    team_slug: str = Field(..., description="Team slug")
    permission: str = Field(..., description="Permission level")
    status: str = Field(..., description="Operation status")
