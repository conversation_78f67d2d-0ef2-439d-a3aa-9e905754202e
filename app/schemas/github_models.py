"""GitHub API response models aligned with official GitHub API schemas."""

from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field


class GitHubUser(BaseModel):
    """GitHub user model matching official API schema."""
    id: int
    node_id: str
    login: str
    avatar_url: str
    gravatar_id: Optional[str] = ""
    url: str
    html_url: str
    followers_url: str
    following_url: str
    gists_url: str
    starred_url: str
    subscriptions_url: str
    organizations_url: str
    repos_url: str
    events_url: str
    received_events_url: str
    type: str
    site_admin: bool
    # Additional fields for detailed user info
    name: Optional[str] = None
    email: Optional[str] = None
    bio: Optional[str] = None
    company: Optional[str] = None
    location: Optional[str] = None
    blog: Optional[str] = None
    twitter_username: Optional[str] = None
    public_repos: Optional[int] = None
    public_gists: Optional[int] = None
    followers: Optional[int] = None
    following: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class GitHubOrganization(BaseModel):
    """GitHub organization model matching official API schema."""
    login: str
    id: int
    node_id: str
    url: str
    repos_url: str
    events_url: str
    hooks_url: str
    issues_url: str
    members_url: str
    public_members_url: str
    avatar_url: str
    description: Optional[str] = None
    name: Optional[str] = None
    company: Optional[str] = None
    blog: Optional[str] = None
    location: Optional[str] = None
    email: Optional[str] = None
    is_verified: Optional[bool] = None
    has_organization_projects: Optional[bool] = None
    has_repository_projects: Optional[bool] = None
    public_repos: Optional[int] = None
    public_gists: Optional[int] = None
    followers: Optional[int] = None
    following: Optional[int] = None
    html_url: str
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    type: str = "Organization"


class GitHubPermissions(BaseModel):
    """GitHub permissions model."""
    admin: bool = False
    maintain: Optional[bool] = None
    push: bool = False
    triage: Optional[bool] = None
    pull: bool = True


class GitHubRepository(BaseModel):
    """GitHub repository model matching official API schema."""
    id: int
    node_id: str
    name: str
    full_name: str
    owner: GitHubUser
    private: bool
    html_url: str
    description: Optional[str] = None
    fork: bool
    url: str
    archive_url: str
    assignees_url: str
    blobs_url: str
    branches_url: str
    collaborators_url: str
    comments_url: str
    commits_url: str
    compare_url: str
    contents_url: str
    contributors_url: str
    deployments_url: str
    downloads_url: str
    events_url: str
    forks_url: str
    git_commits_url: str
    git_refs_url: str
    git_tags_url: str
    git_url: str
    issue_comment_url: str
    issue_events_url: str
    issues_url: str
    keys_url: str
    labels_url: str
    languages_url: str
    merges_url: str
    milestones_url: str
    notifications_url: str
    pulls_url: str
    releases_url: str
    ssh_url: str
    stargazers_url: str
    statuses_url: str
    subscribers_url: str
    subscription_url: str
    tags_url: str
    teams_url: str
    trees_url: str
    clone_url: str
    mirror_url: Optional[str] = None
    hooks_url: str
    svn_url: str
    homepage: Optional[str] = None
    language: Optional[str] = None
    forks_count: int
    stargazers_count: int
    watchers_count: int
    size: int
    default_branch: str
    open_issues_count: int
    is_template: Optional[bool] = None
    topics: Optional[List[str]] = None
    has_issues: bool = True
    has_projects: bool = True
    has_wiki: bool = True
    has_pages: bool = False
    has_downloads: bool = True
    has_discussions: Optional[bool] = None
    archived: bool = False
    disabled: bool = False
    visibility: str = "public"
    pushed_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    permissions: Optional[GitHubPermissions] = None
    # Additional fields
    allow_rebase_merge: Optional[bool] = None
    allow_squash_merge: Optional[bool] = None
    allow_merge_commit: Optional[bool] = None
    allow_auto_merge: Optional[bool] = None
    delete_branch_on_merge: Optional[bool] = None
    subscribers_count: Optional[int] = None
    network_count: Optional[int] = None


class GitHubTeam(BaseModel):
    """GitHub team model matching official API schema."""
    id: int
    node_id: str
    url: str
    html_url: str
    name: str
    slug: str
    description: Optional[str] = None
    privacy: str = "closed"
    notification_setting: str = "notifications_enabled"
    permission: str = "pull"
    members_url: str
    repositories_url: str
    parent: Optional['GitHubTeam'] = None
    members_count: Optional[int] = None
    repos_count: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    organization: Optional[GitHubOrganization] = None


class GitHubTeamMembership(BaseModel):
    """GitHub team membership model."""
    url: str
    role: str = "member"  # member, maintainer
    state: str = "active"  # active, pending


class GitHubRepositoryTeamPermission(BaseModel):
    """GitHub repository team permission model."""
    permission: str  # pull, push, maintain, admin
    role_name: Optional[str] = None


class GitHubPagination(BaseModel):
    """GitHub-style pagination information."""
    page: int = 1
    per_page: int = 30
    total: Optional[int] = None
    total_pages: Optional[int] = None
    has_next: bool = False
    has_prev: bool = False
    # GitHub Link header style
    first_url: Optional[str] = None
    prev_url: Optional[str] = None
    next_url: Optional[str] = None
    last_url: Optional[str] = None


class GitHubRepositoryListResponse(BaseModel):
    """Repository list response model."""
    repositories: List[GitHubRepository]
    pagination: GitHubPagination


class GitHubUserListResponse(BaseModel):
    """User list response model."""
    users: List[GitHubUser]
    pagination: GitHubPagination


class GitHubTeamListResponse(BaseModel):
    """Team list response model."""
    teams: List[GitHubTeam]
    pagination: GitHubPagination


class GitHubRepositoryTeamsResponse(BaseModel):
    """Repository teams response model."""
    repository: Dict[str, Any]
    teams: List[Dict[str, Any]]


class GitHubUserTeamsResponse(BaseModel):
    """User teams response model."""
    user: GitHubUser
    teams: List[GitHubTeam]


class GitHubTeamMembersResponse(BaseModel):
    """Team members response model."""
    team: GitHubTeam
    members: List[GitHubUser]


class GitHubTeamRepositoriesResponse(BaseModel):
    """Team repositories response model."""
    team: GitHubTeam
    repositories: List[GitHubRepository]


# Update forward references
GitHubTeam.model_rebuild()
