"""GitHub integration test endpoints."""

from typing import Dict, Any

from fastapi import APIRouter, Depends, HTTPException, status

from app.api import deps
from app.core.logging import get_logger
from app.core.security.jwt import OIDCTokenPayload
from app.schemas.api import ApiResponse
from app.services.github.service_factory import test_github_connection
from app.services.github.repository_service import GitHubRepositoryService
from app.services.github.user_service import GitHubUserService
from app.services.github.team_service import GitHubTeamService

logger = get_logger("endpoints.github")

router = APIRouter()


@router.get("/test-connection", response_model=ApiResponse[Dict[str, Any]])
async def test_github_connection_endpoint(
) -> ApiResponse[Dict[str, Any]]:
    """Test GitHub API connection and authentication."""
    try:
        result = await test_github_connection()
        
        if result['success']:
            logger.info(f"GitHub connection test successful with {result['auth_method']} authentication")
            return ApiResponse(
                success=True,
                data=result,
                error=None
            )
        else:
            logger.error(f"GitHub connection test failed: {result.get('error')}")
            return ApiResponse(
                success=False,
                data=None,
                error={
                    "code": "GITHUB_CONNECTION_FAILED",
                    "message": "GitHub connection test failed",
                    "details": result
                }
            )
    except Exception as e:
        logger.error(f"GitHub connection test error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to test GitHub connection"
        )


@router.get("/repositories", response_model=ApiResponse[Dict[str, Any]])
async def list_repositories(
    page: int = 1,
    per_page: int = 30,
    search: str = None,
    visibility: str = "all"
) -> ApiResponse[Dict[str, Any]]:
    """List organization repositories."""
    try:
        repo_service = GitHubRepositoryService()
        result = await repo_service.list_repositories(
            page=page,
            per_page=per_page,
            search=search,
            visibility=visibility
        )
        
        logger.info(f"Listed {len(result['repositories'])} repositories")
        return ApiResponse(
            success=True,
            data=result,
            error=None
        )
    except Exception as e:
        logger.error(f"Failed to list repositories: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list repositories"
        )


@router.get("/repositories/{repo_name}/teams", response_model=ApiResponse[Dict[str, Any]])
async def get_repository_teams(
    repo_name: str
) -> ApiResponse[Dict[str, Any]]:
    """Get teams with access to a repository."""
    try:
        repo_service = GitHubRepositoryService()
        result = await repo_service.get_repository_teams(repo_name)
        
        logger.info(f"Retrieved teams for repository {repo_name}")
        return ApiResponse(
            success=True,
            data=result,
            error=None
        )
    except Exception as e:
        logger.error(f"Failed to get repository teams for {repo_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get teams for repository {repo_name}"
        )


@router.get("/users", response_model=ApiResponse[Dict[str, Any]])
async def list_users(
    page: int = 1,
    per_page: int = 30,
    search: str = None,
    role: str = "all"
) -> ApiResponse[Dict[str, Any]]:
    """List organization users."""
    try:
        user_service = GitHubUserService()
        result = await user_service.list_organization_members(
            page=page,
            per_page=per_page,
            role=role,
            search=search
        )
        
        logger.info(f"Listed {len(result['users'])} users")
        return ApiResponse(
            success=True,
            data=result,
            error=None
        )
    except Exception as e:
        logger.error(f"Failed to list users: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list users"
        )


@router.get("/users/{username}/teams", response_model=ApiResponse[Dict[str, Any]])
async def get_user_teams(
    username: str
) -> ApiResponse[Dict[str, Any]]:
    """Get user's team memberships."""
    try:
        user_service = GitHubUserService()
        result = await user_service.get_user_teams(username)
        
        logger.info(f"Retrieved teams for user {username}")
        return ApiResponse(
            success=True,
            data=result,
            error=None
        )
    except Exception as e:
        logger.error(f"Failed to get user teams for {username}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get teams for user {username}"
        )


@router.get("/teams", response_model=ApiResponse[Dict[str, Any]])
async def list_teams(
    page: int = 1,
    per_page: int = 30,
    search: str = None
) -> ApiResponse[Dict[str, Any]]:
    """List organization teams."""
    try:
        team_service = GitHubTeamService()
        result = await team_service.list_teams(
            page=page,
            per_page=per_page,
            search=search
        )
        
        logger.info(f"Listed {len(result['teams'])} teams")
        return ApiResponse(
            success=True,
            data=result,
            error=None
        )
    except Exception as e:
        logger.error(f"Failed to list teams: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list teams"
        )


@router.get("/teams/{team_slug}/members", response_model=ApiResponse[Dict[str, Any]])
async def get_team_members(
    team_slug: str,
    current_user: OIDCTokenPayload = Depends(deps.get_current_user)
) -> ApiResponse[Dict[str, Any]]:
    """Get team members."""
    try:
        team_service = GitHubTeamService()
        result = await team_service.get_team_members(team_slug)
        
        logger.info(f"Retrieved members for team {team_slug}")
        return ApiResponse(
            success=True,
            data=result,
            error=None
        )
    except Exception as e:
        logger.error(f"Failed to get team members for {team_slug}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get members for team {team_slug}"
        )
