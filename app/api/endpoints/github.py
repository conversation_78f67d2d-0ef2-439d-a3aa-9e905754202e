"""GitHub integration API endpoints."""

from typing import Dict, Any

from fastapi import APIRouter, Depends, HTTPException, status

from app.api import deps
from app.core.logging import get_logger
from app.core.security.jwt import OIDCTokenPayload
from app.schemas.api import ApiResponse
from app.schemas.github import (
    BulkRepositoryTeamAssignRequest,
    BulkUserTeamAssignRequest,
    AddUserToTeamRequest,
    AddTeamToRepositoryRequest,
    BulkRepositoryTeamAssignResponse,
    BulkUserTeamAssignResponse,
    TeamMembershipResponse,
    RepositoryTeamPermissionResponse
)
from app.services.github.service_factory import test_github_connection
from app.services.github.repository_service import GitHubRepositoryService
from app.services.github.user_service import GitHubUserService
from app.services.github.team_service import GitHubTeamService

logger = get_logger("endpoints.github")

router = APIRouter()


@router.get("/test-connection", response_model=ApiResponse[Dict[str, Any]])
async def test_github_connection_endpoint(
    current_user: OIDCTokenPayload = Depends(deps.verify_token)
) -> ApiResponse[Dict[str, Any]]:
    """Test GitHub API connection and authentication."""
    try:
        result = await test_github_connection()

        if result['success']:
            logger.info(f"GitHub connection test successful with {result['auth_method']} authentication")
            return ApiResponse(
                success=True,
                data=result,
                error=None
            )
        else:
            logger.error(f"GitHub connection test failed: {result.get('error')}")
            return ApiResponse(
                success=False,
                data=None,
                error={
                    "code": "GITHUB_CONNECTION_FAILED",
                    "message": "GitHub connection test failed",
                    "details": result
                }
            )
    except Exception as e:
        logger.error(f"GitHub connection test error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to test GitHub connection"
        )


@router.get("/repositories", response_model=ApiResponse[Dict[str, Any]])
async def list_repositories(
    page: int = 1,
    per_page: int = 30,
    search: str = None,
    visibility: str = "all",
    current_user: OIDCTokenPayload = Depends(deps.verify_token)
) -> ApiResponse[Dict[str, Any]]:
    """List organization repositories."""
    try:
        repo_service = GitHubRepositoryService()
        result = await repo_service.list_repositories(
            page=page,
            per_page=per_page,
            search=search,
            visibility=visibility
        )

        logger.info(f"Listed {len(result['repositories'])} repositories")
        return ApiResponse(
            success=True,
            data=result,
            error=None
        )
    except Exception as e:
        logger.error(f"Failed to list repositories: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list repositories"
        )


@router.get("/repositories/{repo_name}/teams", response_model=ApiResponse[Dict[str, Any]])
async def get_repository_teams(
    repo_name: str,
    current_user: OIDCTokenPayload = Depends(deps.verify_token)
) -> ApiResponse[Dict[str, Any]]:
    """Get teams with access to a repository."""
    try:
        repo_service = GitHubRepositoryService()
        result = await repo_service.get_repository_teams(repo_name)

        logger.info(f"Retrieved teams for repository {repo_name}")
        return ApiResponse(
            success=True,
            data=result,
            error=None
        )
    except Exception as e:
        logger.error(f"Failed to get repository teams for {repo_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get teams for repository {repo_name}"
        )


@router.get("/users", response_model=ApiResponse[Dict[str, Any]])
async def list_users(
    page: int = 1,
    per_page: int = 30,
    search: str = None,
    role: str = "all",
    current_user: OIDCTokenPayload = Depends(deps.verify_token)
) -> ApiResponse[Dict[str, Any]]:
    """List organization users."""
    try:
        user_service = GitHubUserService()
        result = await user_service.list_organization_members(
            page=page,
            per_page=per_page,
            role=role,
            search=search
        )

        logger.info(f"Listed {len(result['users'])} users")
        return ApiResponse(
            success=True,
            data=result,
            error=None
        )
    except Exception as e:
        logger.error(f"Failed to list users: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list users"
        )


@router.get("/users/{username}/teams", response_model=ApiResponse[Dict[str, Any]])
async def get_user_teams(
    username: str,
    current_user: OIDCTokenPayload = Depends(deps.verify_token)
) -> ApiResponse[Dict[str, Any]]:
    """Get user's team memberships."""
    try:
        user_service = GitHubUserService()
        result = await user_service.get_user_teams(username)

        logger.info(f"Retrieved teams for user {username}")
        return ApiResponse(
            success=True,
            data=result,
            error=None
        )
    except Exception as e:
        logger.error(f"Failed to get user teams for {username}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get teams for user {username}"
        )


@router.get("/users/{username}/details", response_model=ApiResponse[Dict[str, Any]])
async def get_user_with_details(
    username: str,
    current_user: OIDCTokenPayload = Depends(deps.verify_token)
) -> ApiResponse[Dict[str, Any]]:
    """Get detailed user information (makes additional API call for complete data)."""
    try:
        user_service = GitHubUserService()
        result = await user_service.get_user_with_details(username)

        logger.info(f"Retrieved detailed information for user {username}")
        return ApiResponse(
            success=True,
            data=result,
            error=None
        )
    except Exception as e:
        logger.error(f"Failed to get user details for {username}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get details for user {username}"
        )


@router.get("/teams", response_model=ApiResponse[Dict[str, Any]])
async def list_teams(
    page: int = 1,
    per_page: int = 30,
    search: str = None,
    current_user: OIDCTokenPayload = Depends(deps.verify_token)
) -> ApiResponse[Dict[str, Any]]:
    """List organization teams."""
    try:
        team_service = GitHubTeamService()
        result = await team_service.list_teams(
            page=page,
            per_page=per_page,
            search=search
        )

        logger.info(f"Listed {len(result['teams'])} teams")
        return ApiResponse(
            success=True,
            data=result,
            error=None
        )
    except Exception as e:
        logger.error(f"Failed to list teams: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list teams"
        )


@router.get("/teams/{team_slug}/members", response_model=ApiResponse[Dict[str, Any]])
async def get_team_members(
    team_slug: str,
    current_user: OIDCTokenPayload = Depends(deps.verify_token)
) -> ApiResponse[Dict[str, Any]]:
    """Get team members."""
    try:
        team_service = GitHubTeamService()
        result = await team_service.get_team_members(team_slug)

        logger.info(f"Retrieved members for team {team_slug}")
        return ApiResponse(
            success=True,
            data=result,
            error=None
        )
    except Exception as e:
        logger.error(f"Failed to get team members for {team_slug}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get members for team {team_slug}"
        )


# ============================================================================
# Repository Management Endpoints
# ============================================================================

@router.post("/repositories/teams/assign", response_model=ApiResponse[BulkRepositoryTeamAssignResponse])
async def bulk_assign_teams_to_repositories(
    request: BulkRepositoryTeamAssignRequest,
    current_user: OIDCTokenPayload = Depends(deps.verify_token)
) -> ApiResponse[BulkRepositoryTeamAssignResponse]:
    """Bulk assign teams to multiple repositories."""
    try:
        repo_service = GitHubRepositoryService()
        result = await repo_service.bulk_assign_teams_to_repositories(
            repository_names=request.repository_names,
            team_assignments=[{
                'team_slug': assignment.team_slug,
                'permission': assignment.permission
            } for assignment in request.team_assignments],
            replace_existing=request.replace_existing
        )

        logger.info(f"Bulk assigned teams to repositories: {result['assignments_created']} created, {result['assignments_failed']} failed")
        return ApiResponse(
            success=True,
            data=BulkRepositoryTeamAssignResponse(**result),
            error=None
        )
    except Exception as e:
        logger.error(f"Failed to bulk assign teams to repositories: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to bulk assign teams to repositories"
        )


@router.put("/teams/{team_slug}/repos/{owner}/{repo}", response_model=ApiResponse[RepositoryTeamPermissionResponse])
async def add_team_to_repository(
    team_slug: str,
    owner: str,
    repo: str,
    request: AddTeamToRepositoryRequest,
    current_user: OIDCTokenPayload = Depends(deps.verify_token)
) -> ApiResponse[RepositoryTeamPermissionResponse]:
    """Add team to repository with specific permission."""
    try:
        repo_service = GitHubRepositoryService()
        result = await repo_service.add_team_to_repository(
            repo_name=repo,
            team_slug=team_slug,
            permission=request.permission
        )

        logger.info(f"Added team {team_slug} to repository {repo} with {request.permission} permission")
        return ApiResponse(
            success=True,
            data=RepositoryTeamPermissionResponse(**result),
            error=None
        )
    except Exception as e:
        logger.error(f"Failed to add team {team_slug} to repository {repo}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to add team to repository"
        )


@router.delete("/teams/{team_slug}/repos/{owner}/{repo}", response_model=ApiResponse[Dict[str, Any]])
async def remove_team_from_repository(
    team_slug: str,
    owner: str,
    repo: str,
    current_user: OIDCTokenPayload = Depends(deps.verify_token)
) -> ApiResponse[Dict[str, Any]]:
    """Remove team from repository."""
    try:
        repo_service = GitHubRepositoryService()
        result = await repo_service.remove_team_from_repository(
            repo_name=repo,
            team_slug=team_slug
        )

        logger.info(f"Removed team {team_slug} from repository {repo}")
        return ApiResponse(
            success=True,
            data=result,
            error=None
        )
    except Exception as e:
        logger.error(f"Failed to remove team {team_slug} from repository {repo}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to remove team from repository"
        )


# ============================================================================
# User Management Endpoints
# ============================================================================

@router.post("/users/teams/assign", response_model=ApiResponse[BulkUserTeamAssignResponse])
async def bulk_assign_users_to_teams(
    request: BulkUserTeamAssignRequest,
    current_user: OIDCTokenPayload = Depends(deps.verify_token)
) -> ApiResponse[BulkUserTeamAssignResponse]:
    """Bulk assign users to multiple teams."""
    try:
        user_service = GitHubUserService()
        result = await user_service.bulk_assign_users_to_teams(
            usernames=request.usernames,
            team_slugs=request.team_slugs,
            role=request.role,
            replace_existing=request.replace_existing
        )

        logger.info(f"Bulk assigned users to teams: {result['assignments_created']} created, {result['assignments_failed']} failed")
        return ApiResponse(
            success=True,
            data=BulkUserTeamAssignResponse(**result),
            error=None
        )
    except Exception as e:
        logger.error(f"Failed to bulk assign users to teams: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to bulk assign users to teams"
        )


@router.put("/teams/{team_slug}/memberships/{username}", response_model=ApiResponse[TeamMembershipResponse])
async def add_user_to_team(
    team_slug: str,
    username: str,
    request: AddUserToTeamRequest,
    current_user: OIDCTokenPayload = Depends(deps.verify_token)
) -> ApiResponse[TeamMembershipResponse]:
    """Add user to team with specific role."""
    try:
        user_service = GitHubUserService()
        result = await user_service.add_user_to_team(
            username=username,
            team_slug=team_slug,
            role=request.role
        )

        logger.info(f"Added user {username} to team {team_slug} with {request.role} role")
        return ApiResponse(
            success=True,
            data=TeamMembershipResponse(
                url=f"https://api.github.com/teams/{team_slug}/memberships/{username}",
                role=result['role'],
                state=result['state']
            ),
            error=None
        )
    except Exception as e:
        logger.error(f"Failed to add user {username} to team {team_slug}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to add user to team"
        )


@router.delete("/teams/{team_slug}/memberships/{username}", response_model=ApiResponse[Dict[str, Any]])
async def remove_user_from_team(
    team_slug: str,
    username: str,
    current_user: OIDCTokenPayload = Depends(deps.verify_token)
) -> ApiResponse[Dict[str, Any]]:
    """Remove user from team."""
    try:
        user_service = GitHubUserService()
        result = await user_service.remove_user_from_team(
            username=username,
            team_slug=team_slug
        )

        logger.info(f"Removed user {username} from team {team_slug}")
        return ApiResponse(
            success=True,
            data=result,
            error=None
        )
    except Exception as e:
        logger.error(f"Failed to remove user {username} from team {team_slug}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to remove user from team"
        )


# ============================================================================
# Team Management Endpoints
# ============================================================================

@router.get("/teams/{team_slug}/repos", response_model=ApiResponse[Dict[str, Any]])
async def get_team_repositories(
    team_slug: str,
    current_user: OIDCTokenPayload = Depends(deps.verify_token)
) -> ApiResponse[Dict[str, Any]]:
    """Get repositories accessible to a team."""
    try:
        team_service = GitHubTeamService()
        result = await team_service.get_team_repositories(team_slug)

        logger.info(f"Retrieved repositories for team {team_slug}")
        return ApiResponse(
            success=True,
            data=result,
            error=None
        )
    except Exception as e:
        logger.error(f"Failed to get team repositories for {team_slug}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get repositories for team {team_slug}"
        )
