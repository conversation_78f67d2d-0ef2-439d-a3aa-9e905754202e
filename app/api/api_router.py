from fastapi import APIRouter

from app.api import api_messages
from app.api.endpoints import auth, emails, tasks, users, jobs, kibana, github

# Router for authentication endpoints (only registration remains)
auth_router = APIRouter()
auth_router.include_router(auth.router, prefix="/auth", tags=["auth"])

api_router = APIRouter(
    responses={
        401: {
            "description": "No `Authorization` bearer token header or token is invalid",
            "content": {
                "application/json": {
                    "examples": {
                        "not authenticated": {
                            "summary": "No authorization token header",
                            "value": {"detail": "Not authenticated"},
                        },
                        "token expired": {
                            "summary": "Token has expired",
                            "value": {"detail": "Token has expired"},
                        },
                        "invalid audience": {
                            "summary": "Token has an invalid audience",
                            "value": {"detail": "Token has an invalid audience"},
                        },
                        "invalid issuer": {
                            "summary": "Token has an invalid issuer",
                            "value": {"detail": "Token has an invalid issuer"},
                        },
                        "invalid token": {
                            "summary": "Token validation failed, decode failed, it may be malformed",
                            "value": {"detail": "Token invalid: {detailed error msg}"},
                        },
                        "invalid payload": {
                            "summary": "Token payload is invalid",
                            "value": {"detail": "Token payload invalid: {detailed error msg}"},
                        },
                    }
                }
            },
        },
        404: {
            "description": "Resource not found",
            "content": {
                "application/json": {
                    "examples": {
                        "user not found": {
                            "summary": "User not found",
                            "value": {"detail": "User not found"},
                        },
                        "task not found": {
                            "summary": "Task not found",
                            "value": {"detail": "Task not found"},
                        },
                        "email notification not found": {
                            "summary": "Email notification not found",
                            "value": {"detail": "Email notification not found"},
                        },
                    }
                }
            },
        },
    }
)
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(tasks.router, prefix="/tasks", tags=["tasks"])
api_router.include_router(emails.router, prefix="/emails", tags=["emails"])
api_router.include_router(jobs.router, prefix="/jobs", tags=["jobs"])
api_router.include_router(kibana.router, prefix="/dashboards", tags=["kibana"])
api_router.include_router(github.router, prefix="/github", tags=["github"])
