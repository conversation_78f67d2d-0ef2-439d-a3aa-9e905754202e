# File with environment variables and general configuration logic.
# Env variables are combined in nested groups like "Security", "Database" etc.
# So environment variable (case-insensitive) for jwt_secret_key will be "security__jwt_secret_key"
#
# Pydantic priority ordering:
#
# 1. (Most important, will overwrite everything) - environment variables
# 2. `.env` file in root folder of project
# 3. Default values
#
# "sqlalchemy_database_uri" is computed field that will create valid database URL
#
# See https://pydantic-docs.helpmanual.io/usage/settings/
# Note, complex types like lists are read as json-encoded strings.


from functools import lru_cache
from pathlib import Path

from pydantic import AnyHttpUrl, BaseModel, SecretStr, computed_field, Field
from pydantic_settings import BaseSettings, SettingsConfigDict
from sqlalchemy.engine.url import URL

PROJECT_DIR = Path(__file__).parent.parent.parent


class Security(BaseModel):
    # OIDC settings
    oidc_issuer: str = "https://your-oidc-provider.com"  # OIDC provider URL
    oidc_client_id: str = "your-client-id"  # Client ID registered with the OIDC provider
    oidc_jwks_uri: str = "https://your-oidc-provider.com/.well-known/jwks.json"  # JWKS URI for token verification

    # Legacy JWT settings (kept for backward compatibility)
    jwt_issuer: str = "my-app"
    jwt_secret_key: SecretStr
    jwt_access_token_expire_secs: int = 24 * 3600  # 1d

    # General security settings
    password_bcrypt_rounds: int = 12
    allowed_hosts: list[str] = ["localhost", "127.0.0.1"]
    backend_cors_origins: list[AnyHttpUrl] = []

    # Kibana proxy settings
    secret_key: SecretStr


class Database(BaseModel):
    hostname: str = "postgres"
    username: str = "postgres"
    password: SecretStr
    port: int = 5432
    db: str = "postgres"


class Redis(BaseModel):
    host: str = "localhost"
    port: int = 6379
    db: int = 0
    password: str = ""


class Celery(BaseModel):
    broker_url: str = Field(default="redis://localhost:6379/0")
    result_backend: str = Field(default="redis://localhost:6379/0")


class Email(BaseModel):
    smtp_server: str = "smtp.gmail.com"
    smtp_port: int = 587
    smtp_username: str = ""
    smtp_password: str = ""
    use_tls: bool = True
    use_ssl: bool = False
    default_sender: str = ""
    default_sender_name: str = "TaskMaster Notifications"


class GitHub(BaseModel):
    # Authentication method: 'github_app' or 'personal_access_token'
    auth_method: str = "github_app"

    # GitHub App settings
    app_id: str = ""
    private_key_path: str = ""
    private_key: str = ""  # Alternative to private_key_path
    installation_id: str = ""

    # Personal Access Token settings
    personal_access_token: str = ""

    # Organization settings
    org_name: str = ""

    # API settings
    api_base_url: str = "https://api.github.com"
    api_timeout: int = 30
    api_retry_attempts: int = 3


class Settings(BaseSettings):
    security: Security
    database: Database
    redis: Redis
    celery: Celery
    email: Email
    github: GitHub

    @computed_field  # type: ignore[prop-decorator]
    @property
    def sqlalchemy_database_uri(self) -> URL:
        return URL.create(
            drivername="postgresql+asyncpg",
            username=self.database.username,
            password=self.database.password.get_secret_value(),
            host=self.database.hostname,
            port=self.database.port,
            database=self.database.db,
        )

    model_config = SettingsConfigDict(
        env_file=f"{PROJECT_DIR}/.env",
        case_sensitive=False,
        env_nested_delimiter="__",
    )


@lru_cache(maxsize=1)
def get_settings() -> Settings:
    return Settings()  # type: ignore
