#!/usr/bin/env python3
"""
Test script for GitHub integration.

This script tests the GitHub authentication and basic API functionality.
Run this script to verify your GitHub configuration is working correctly.

Usage:
    python scripts/test_github_integration.py
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the app directory to the Python path
app_dir = Path(__file__).parent.parent / "app"
sys.path.insert(0, str(app_dir))

from app.services.github.service_factory import test_github_connection, get_github_org_name
from app.services.github.repository_service import GitHubRepositoryService
from app.services.github.user_service import GitHubUserService
from app.services.github.team_service import GitHubTeamService


async def test_connection():
    """Test GitHub API connection."""
    print("🔗 Testing GitHub API connection...")
    
    try:
        result = await test_github_connection()
        
        if result['success']:
            print(f"✅ Connection successful!")
            print(f"   Authentication method: {result['auth_method']}")
            print(f"   Organization: {result.get('organization', 'N/A')}")
            print(f"   Rate limit remaining: {result['rate_limit']['remaining']}")
            return True
        else:
            print(f"❌ Connection failed: {result.get('error', 'Unknown error')}")
            return False
    except Exception as e:
        print(f"❌ Connection test failed: {e}")
        return False


async def test_repositories():
    """Test repository listing."""
    print("\n📁 Testing repository listing...")
    
    try:
        repo_service = GitHubRepositoryService()
        result = await repo_service.list_repositories(per_page=5)
        
        repos = result['repositories']
        print(f"✅ Found {len(repos)} repositories (showing first 5)")
        
        for repo in repos[:3]:  # Show first 3
            print(f"   - {repo['name']} ({'private' if repo['private'] else 'public'})")
        
        if len(repos) > 3:
            print(f"   ... and {len(repos) - 3} more")
        
        return True
    except Exception as e:
        print(f"❌ Repository listing failed: {e}")
        return False


async def test_users():
    """Test user listing."""
    print("\n👥 Testing user listing...")
    
    try:
        user_service = GitHubUserService()
        result = await user_service.list_organization_members(per_page=5)
        
        users = result['users']
        print(f"✅ Found {len(users)} users (showing first 5)")
        
        for user in users[:3]:  # Show first 3
            print(f"   - {user['login']} ({user.get('name', 'No name')})")
        
        if len(users) > 3:
            print(f"   ... and {len(users) - 3} more")
        
        return True
    except Exception as e:
        print(f"❌ User listing failed: {e}")
        return False


async def test_teams():
    """Test team listing."""
    print("\n🏢 Testing team listing...")
    
    try:
        team_service = GitHubTeamService()
        result = await team_service.list_teams(per_page=5)
        
        teams = result['teams']
        print(f"✅ Found {len(teams)} teams (showing first 5)")
        
        for team in teams[:3]:  # Show first 3
            print(f"   - {team['name']} ({team['members_count']} members)")
        
        if len(teams) > 3:
            print(f"   ... and {len(teams) - 3} more")
        
        return True
    except Exception as e:
        print(f"❌ Team listing failed: {e}")
        return False


async def test_repository_teams():
    """Test repository team access."""
    print("\n🔐 Testing repository team access...")
    
    try:
        repo_service = GitHubRepositoryService()
        repos_result = await repo_service.list_repositories(per_page=1)
        
        if not repos_result['repositories']:
            print("⚠️ No repositories found to test team access")
            return True
        
        repo_name = repos_result['repositories'][0]['name']
        teams_result = await repo_service.get_repository_teams(repo_name)
        
        teams = teams_result['teams']
        print(f"✅ Repository '{repo_name}' has {len(teams)} team(s) with access")
        
        for team in teams:
            print(f"   - {team['name']} ({team['permission']} access)")
        
        return True
    except Exception as e:
        print(f"❌ Repository team access test failed: {e}")
        return False


def check_environment():
    """Check environment configuration."""
    print("🔧 Checking environment configuration...")
    
    auth_method = os.getenv('GITHUB__AUTH_METHOD', 'github_app')
    org_name = os.getenv('GITHUB__ORG_NAME')
    
    print(f"   Authentication method: {auth_method}")
    print(f"   Organization: {org_name or 'NOT SET'}")
    
    if not org_name:
        print("❌ GITHUB__ORG_NAME is not set")
        return False
    
    if auth_method == 'github_app':
        app_id = os.getenv('GITHUB__APP_ID')
        installation_id = os.getenv('GITHUB__INSTALLATION_ID')
        private_key_path = os.getenv('GITHUB__PRIVATE_KEY_PATH')
        private_key = os.getenv('GITHUB__PRIVATE_KEY')
        
        if not app_id:
            print("❌ GITHUB__APP_ID is not set")
            return False
        if not installation_id:
            print("❌ GITHUB__INSTALLATION_ID is not set")
            return False
        if not private_key_path and not private_key:
            print("❌ Neither GITHUB__PRIVATE_KEY_PATH nor GITHUB__PRIVATE_KEY is set")
            return False
        if private_key_path and not os.path.exists(private_key_path):
            print(f"❌ Private key file not found: {private_key_path}")
            return False
        
        print("✅ GitHub App configuration looks good")
    
    elif auth_method == 'personal_access_token':
        token = os.getenv('GITHUB__PERSONAL_ACCESS_TOKEN')
        
        if not token:
            print("❌ GITHUB__PERSONAL_ACCESS_TOKEN is not set")
            return False
        
        print("✅ Personal Access Token configuration looks good")
    
    else:
        print(f"❌ Invalid authentication method: {auth_method}")
        return False
    
    return True


async def main():
    """Main test function."""
    print("🚀 GitHub Integration Test")
    print("=" * 50)
    
    # Check environment
    if not check_environment():
        print("\n❌ Environment configuration is invalid. Please check your .env file.")
        print("   See .env.github.example for configuration examples.")
        return False
    
    # Test connection
    if not await test_connection():
        print("\n❌ GitHub connection failed. Please check your configuration.")
        return False
    
    # Test basic functionality
    tests = [
        test_repositories,
        test_users,
        test_teams,
        test_repository_teams
    ]
    
    results = []
    for test in tests:
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append(False)
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary")
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✅ All {total} tests passed!")
        print("\n🎉 GitHub integration is working correctly!")
        return True
    else:
        print(f"⚠️ {passed}/{total} tests passed")
        print("\n❌ Some tests failed. Please check your configuration and permissions.")
        return False


if __name__ == "__main__":
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    # Run tests
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
