#!/usr/bin/env python3
"""
Test script for GitHub API alignment validation.

This script validates that our GitHub services properly align with the official
GitHub API response schemas and include all standard GitHub fields.

Usage:
    python scripts/test_github_api_alignment.py
"""

import asyncio
import sys
from pathlib import Path

# Add the app directory to the Python path
app_dir = Path(__file__).parent.parent / "app"
sys.path.insert(0, str(app_dir))

from app.services.github.repository_service import GitHubRepositoryService
from app.services.github.user_service import GitHubUserService
from app.services.github.team_service import GitHubTeamService


async def test_repository_api_alignment():
    """Test repository API response alignment with GitHub API."""
    print("📁 Testing Repository API Alignment...")
    
    try:
        repo_service = GitHubRepositoryService()
        result = await repo_service.list_repositories(per_page=1)
        
        if not result['repositories']:
            print("   ⚠️ No repositories found for testing")
            return True
        
        repo = result['repositories'][0]
        
        # Check for essential GitHub API fields
        required_fields = [
            'id', 'node_id', 'name', 'full_name', 'owner', 'private',
            'html_url', 'description', 'fork', 'url', 'created_at', 'updated_at'
        ]
        
        missing_fields = [field for field in required_fields if field not in repo]
        if missing_fields:
            print(f"   ❌ Missing required GitHub API fields: {missing_fields}")
            return False
        
        # Check owner structure
        if 'login' not in repo['owner'] or 'id' not in repo['owner']:
            print("   ❌ Repository owner missing required fields (login, id)")
            return False
        
        print("   ✅ Repository response includes all required GitHub API fields")
        print(f"   ✅ Sample repository: {repo['name']} (ID: {repo['id']})")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Repository API alignment test failed: {e}")
        return False


async def test_user_api_alignment():
    """Test user API response alignment with GitHub API."""
    print("\n👥 Testing User API Alignment...")
    
    try:
        user_service = GitHubUserService()
        result = await user_service.list_organization_members(per_page=1)
        
        if not result['users']:
            print("   ⚠️ No users found for testing")
            return True
        
        user = result['users'][0]
        
        # Check for essential GitHub API fields
        required_fields = [
            'id', 'login', 'avatar_url', 'html_url', 'type'
        ]
        
        missing_fields = [field for field in required_fields if field not in user]
        if missing_fields:
            print(f"   ❌ Missing required GitHub API fields: {missing_fields}")
            return False
        
        # Check for GitHub API URLs
        url_fields = [
            'url', 'followers_url', 'following_url', 'gists_url',
            'starred_url', 'subscriptions_url', 'organizations_url', 'repos_url'
        ]
        
        missing_url_fields = [field for field in url_fields if field not in user]
        if missing_url_fields:
            print(f"   ⚠️ Missing GitHub API URL fields: {missing_url_fields}")
        
        print("   ✅ User response includes required GitHub API fields")
        print(f"   ✅ Sample user: {user['login']} (ID: {user['id']})")
        
        return True
        
    except Exception as e:
        print(f"   ❌ User API alignment test failed: {e}")
        return False


async def test_team_api_alignment():
    """Test team API response alignment with GitHub API."""
    print("\n🏢 Testing Team API Alignment...")
    
    try:
        team_service = GitHubTeamService()
        result = await team_service.list_teams(per_page=1)
        
        if not result['teams']:
            print("   ⚠️ No teams found for testing")
            return True
        
        team = result['teams'][0]
        
        # Check for essential GitHub API fields
        required_fields = [
            'id', 'name', 'slug', 'privacy', 'url', 'html_url'
        ]
        
        missing_fields = [field for field in required_fields if field not in team]
        if missing_fields:
            print(f"   ❌ Missing required GitHub API fields: {missing_fields}")
            return False
        
        # Check for GitHub API URLs
        url_fields = ['members_url', 'repositories_url']
        missing_url_fields = [field for field in url_fields if field not in team]
        if missing_url_fields:
            print(f"   ⚠️ Missing GitHub API URL fields: {missing_url_fields}")
        
        print("   ✅ Team response includes required GitHub API fields")
        print(f"   ✅ Sample team: {team['name']} (ID: {team['id']})")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Team API alignment test failed: {e}")
        return False


async def test_api_headers():
    """Test that API requests use correct headers."""
    print("\n📋 Testing API Headers...")
    
    try:
        from app.services.github.service_factory import get_github_service
        
        github_service = get_github_service()
        headers = await github_service._get_headers()
        
        # Check for required headers
        required_headers = ['Authorization', 'Accept', 'X-GitHub-Api-Version', 'User-Agent']
        missing_headers = [header for header in required_headers if header not in headers]
        
        if missing_headers:
            print(f"   ❌ Missing required headers: {missing_headers}")
            return False
        
        # Check header values
        if headers['Accept'] != 'application/vnd.github+json':
            print(f"   ❌ Incorrect Accept header: {headers['Accept']}")
            return False
        
        if headers['X-GitHub-Api-Version'] != '2022-11-28':
            print(f"   ❌ Incorrect API version: {headers['X-GitHub-Api-Version']}")
            return False
        
        print("   ✅ API headers are correctly configured")
        print(f"   ✅ Accept: {headers['Accept']}")
        print(f"   ✅ API Version: {headers['X-GitHub-Api-Version']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ API headers test failed: {e}")
        return False


async def test_pagination_format():
    """Test pagination format alignment."""
    print("\n📄 Testing Pagination Format...")
    
    try:
        repo_service = GitHubRepositoryService()
        result = await repo_service.list_repositories(page=1, per_page=2)
        
        pagination = result['pagination']
        
        # Check for required pagination fields
        required_fields = ['page', 'per_page', 'total', 'total_pages', 'has_next', 'has_prev']
        missing_fields = [field for field in required_fields if field not in pagination]
        
        if missing_fields:
            print(f"   ❌ Missing pagination fields: {missing_fields}")
            return False
        
        # Validate pagination logic
        if pagination['page'] != 1:
            print(f"   ❌ Incorrect page number: {pagination['page']}")
            return False
        
        if pagination['per_page'] != 2:
            print(f"   ❌ Incorrect per_page: {pagination['per_page']}")
            return False
        
        if pagination['has_prev'] != False:
            print(f"   ❌ Incorrect has_prev for page 1: {pagination['has_prev']}")
            return False
        
        print("   ✅ Pagination format is correct")
        print(f"   ✅ Page: {pagination['page']}/{pagination['total_pages']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Pagination format test failed: {e}")
        return False


async def test_error_handling():
    """Test error handling alignment with GitHub API."""
    print("\n⚠️ Testing Error Handling...")
    
    try:
        from app.services.github.base_service import GitHubAPIError, ResourceNotFound
        
        repo_service = GitHubRepositoryService()
        
        # Test with non-existent repository
        try:
            await repo_service.get_repository_teams("non-existent-repo-12345")
            print("   ❌ Expected ResourceNotFound exception was not raised")
            return False
        except ResourceNotFound:
            print("   ✅ ResourceNotFound exception properly raised for non-existent resource")
        except Exception as e:
            print(f"   ⚠️ Unexpected exception type: {type(e).__name__}: {e}")
        
        print("   ✅ Error handling is working correctly")
        return True
        
    except Exception as e:
        print(f"   ❌ Error handling test failed: {e}")
        return False


async def main():
    """Main test function."""
    print("🔍 GitHub API Alignment Validation")
    print("=" * 50)
    
    # Test all API alignment aspects
    tests = [
        test_api_headers,
        test_repository_api_alignment,
        test_user_api_alignment,
        test_team_api_alignment,
        test_pagination_format,
        test_error_handling
    ]
    
    results = []
    for test in tests:
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append(False)
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 API Alignment Validation Summary")
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✅ All {total} tests passed!")
        print("\n🎉 GitHub API alignment is correct!")
        print("\n📋 Improvements Made:")
        print("   • Updated API headers to use latest GitHub API version (2022-11-28)")
        print("   • Preserved all GitHub API fields in responses")
        print("   • Aligned response structures with official GitHub API schemas")
        print("   • Maintained backward compatibility with custom fields")
        print("   • Improved error handling alignment")
        return True
    else:
        print(f"⚠️ {passed}/{total} tests passed")
        print("\n❌ Some API alignment issues found. Please review the implementation.")
        return False


if __name__ == "__main__":
    # Load environment variables
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        pass
    
    # Run tests
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
