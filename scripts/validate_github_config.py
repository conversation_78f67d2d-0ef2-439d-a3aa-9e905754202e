#!/usr/bin/env python3
"""
GitHub Configuration Validation Script

This script validates your GitHub integration configuration and helps identify
any issues with environment variables or settings.

Usage:
    python scripts/validate_github_config.py
"""

import os
import sys
from pathlib import Path

# Add the app directory to the Python path
app_dir = Path(__file__).parent.parent / "app"
sys.path.insert(0, str(app_dir))


def print_header():
    """Print script header."""
    print("🔧 GitHub Configuration Validation")
    print("=" * 50)


def check_pydantic_config():
    """Check if Pydantic configuration is working correctly."""
    print("📋 Checking Pydantic Configuration...")
    
    try:
        from app.core.config import get_settings
        settings = get_settings()
        
        print("✅ Pydantic settings loaded successfully")
        print(f"   Environment file: {settings.model_config.get('env_file', 'Not specified')}")
        print(f"   Case sensitive: {settings.model_config.get('case_sensitive', 'Not specified')}")
        print(f"   Nested delimiter: {settings.model_config.get('env_nested_delimiter', 'Not specified')}")
        
        return settings
    except Exception as e:
        print(f"❌ Failed to load Pydantic settings: {e}")
        return None


def check_github_config(settings):
    """Check GitHub-specific configuration."""
    print("\n🐙 Checking GitHub Configuration...")
    
    try:
        github_config = settings.github
        
        print(f"   Auth method: {github_config.auth_method}")
        print(f"   Organization: {github_config.org_name or 'NOT SET'}")
        print(f"   API base URL: {github_config.api_base_url}")
        print(f"   API timeout: {github_config.api_timeout}s")
        print(f"   API retry attempts: {github_config.api_retry_attempts}")
        
        # Check authentication-specific config
        if github_config.auth_method == 'github_app':
            print(f"   App ID: {github_config.app_id or 'NOT SET'}")
            print(f"   Installation ID: {github_config.installation_id or 'NOT SET'}")
            print(f"   Private key path: {github_config.private_key_path or 'NOT SET'}")
            print(f"   Private key (env var): {'SET' if github_config.private_key else 'NOT SET'}")
        elif github_config.auth_method == 'personal_access_token':
            print(f"   Personal access token: {'SET' if github_config.personal_access_token else 'NOT SET'}")
        
        return github_config
    except Exception as e:
        print(f"❌ Failed to access GitHub configuration: {e}")
        return None


def check_environment_variables():
    """Check environment variables directly."""
    print("\n🌍 Checking Environment Variables...")
    
    # GitHub configuration variables
    github_vars = [
        'GITHUB__AUTH_METHOD',
        'GITHUB__ORG_NAME',
        'GITHUB__APP_ID',
        'GITHUB__INSTALLATION_ID',
        'GITHUB__PRIVATE_KEY_PATH',
        'GITHUB__PRIVATE_KEY',
        'GITHUB__PERSONAL_ACCESS_TOKEN',
        'GITHUB__API_BASE_URL',
        'GITHUB__API_TIMEOUT',
        'GITHUB__API_RETRY_ATTEMPTS'
    ]
    
    set_vars = []
    unset_vars = []
    
    for var in github_vars:
        value = os.getenv(var)
        if value:
            set_vars.append(var)
            # Don't print sensitive values
            if 'TOKEN' in var or 'KEY' in var:
                print(f"   ✅ {var}: [HIDDEN]")
            else:
                print(f"   ✅ {var}: {value}")
        else:
            unset_vars.append(var)
            print(f"   ❌ {var}: NOT SET")
    
    print(f"\n   Summary: {len(set_vars)} set, {len(unset_vars)} unset")
    return set_vars, unset_vars


def validate_configuration(github_config):
    """Validate the configuration for completeness."""
    print("\n✅ Validating Configuration...")
    
    errors = []
    warnings = []
    
    # Check organization name
    if not github_config.org_name:
        errors.append("Organization name (GITHUB__ORG_NAME) is required")
    
    # Check authentication method
    auth_method = github_config.auth_method.lower()
    if auth_method not in ['github_app', 'personal_access_token']:
        errors.append(f"Invalid authentication method: {auth_method}")
    
    # Validate GitHub App configuration
    if auth_method == 'github_app':
        if not github_config.app_id:
            errors.append("GitHub App ID (GITHUB__APP_ID) is required for GitHub App authentication")
        if not github_config.installation_id:
            errors.append("GitHub App Installation ID (GITHUB__INSTALLATION_ID) is required")
        
        # Check private key
        if not github_config.private_key_path and not github_config.private_key:
            errors.append("Either private key path (GITHUB__PRIVATE_KEY_PATH) or private key content (GITHUB__PRIVATE_KEY) is required")
        elif github_config.private_key_path:
            if not os.path.exists(github_config.private_key_path):
                errors.append(f"Private key file not found: {github_config.private_key_path}")
            else:
                print(f"   ✅ Private key file exists: {github_config.private_key_path}")
    
    # Validate PAT configuration
    elif auth_method == 'personal_access_token':
        if not github_config.personal_access_token:
            errors.append("Personal Access Token (GITHUB__PERSONAL_ACCESS_TOKEN) is required for PAT authentication")
    
    # Check API configuration
    if github_config.api_timeout <= 0:
        warnings.append(f"API timeout should be positive, got: {github_config.api_timeout}")
    
    if github_config.api_retry_attempts < 0:
        warnings.append(f"API retry attempts should be non-negative, got: {github_config.api_retry_attempts}")
    
    # Print results
    if errors:
        print("   ❌ Configuration Errors:")
        for error in errors:
            print(f"      • {error}")
    
    if warnings:
        print("   ⚠️ Configuration Warnings:")
        for warning in warnings:
            print(f"      • {warning}")
    
    if not errors and not warnings:
        print("   ✅ Configuration is valid!")
    
    return len(errors) == 0


def test_github_validation():
    """Test the GitHub validation function."""
    print("\n🧪 Testing GitHub Validation Function...")
    
    try:
        from app.services.github.auth_strategies import validate_github_config
        validate_github_config()
        print("   ✅ GitHub validation passed!")
        return True
    except Exception as e:
        print(f"   ❌ GitHub validation failed: {e}")
        return False


def print_recommendations():
    """Print configuration recommendations."""
    print("\n💡 Recommendations:")
    print("   • Use GitHub App authentication for production environments")
    print("   • Use Personal Access Token for development and testing")
    print("   • Store sensitive values in environment variables, not in code")
    print("   • Regularly rotate Personal Access Tokens (every 90 days)")
    print("   • Use minimal required permissions for both authentication methods")
    print("   • Test your configuration with: python scripts/test_github_integration.py")


def main():
    """Main validation function."""
    print_header()
    
    # Load environment variables from .env file
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("📁 Loaded environment variables from .env file")
    except ImportError:
        print("⚠️ python-dotenv not available, using system environment variables only")
    except Exception as e:
        print(f"⚠️ Failed to load .env file: {e}")
    
    # Check Pydantic configuration
    settings = check_pydantic_config()
    if not settings:
        print("\n❌ Cannot proceed without valid Pydantic settings")
        return False
    
    # Check GitHub configuration
    github_config = check_github_config(settings)
    if not github_config:
        print("\n❌ Cannot proceed without valid GitHub configuration")
        return False
    
    # Check environment variables
    check_environment_variables()
    
    # Validate configuration
    is_valid = validate_configuration(github_config)
    
    # Test validation function
    validation_passed = test_github_validation()
    
    # Print recommendations
    print_recommendations()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Validation Summary")
    
    if is_valid and validation_passed:
        print("✅ Configuration is ready for use!")
        print("\n🚀 Next steps:")
        print("   1. Run: python scripts/test_github_integration.py")
        print("   2. Start the application and test: GET /v1/github/test-connection")
        return True
    else:
        print("❌ Configuration has issues that need to be resolved")
        print("\n🔧 Next steps:")
        print("   1. Fix the configuration errors listed above")
        print("   2. Check the .env.github.example file for reference")
        print("   3. Re-run this validation script")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
