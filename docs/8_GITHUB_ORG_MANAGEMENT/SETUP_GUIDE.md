# GitHub Integration Setup Guide

This guide will help you set up GitHub integration for the TaskMaster application. You can choose between two authentication methods: GitHub App (recommended for production) or Personal Access Token (good for development).

## Quick Start

1. **Choose Authentication Method**
   - **GitHub App**: Recommended for production, better security and rate limits
   - **Personal Access Token**: Simpler setup, good for development and testing

2. **Configure Environment Variables**
   - Copy `.env.github.example` to your `.env` file
   - Fill in the required values based on your chosen authentication method

3. **Test the Integration**
   - Run the test script: `python scripts/test_github_integration.py`
   - Check the API endpoint: `GET /v1/github/test-connection`

## Authentication Methods

### Option 1: GitHub App (Recommended)

#### Step 1: Create GitHub App

1. Go to your GitHub organization settings
2. Navigate to **Developer settings** > **GitHub Apps**
3. Click **New GitHub App**
4. Fill in the required information:
   - **GitHub App name**: `TaskMaster Integration`
   - **Homepage URL**: Your application URL
   - **Webhook URL**: Leave blank for now (optional)

#### Step 2: Set Permissions

Configure the following permissions for your GitHub App:

**Repository permissions:**
- **Administration**: Read & Write
- **Metadata**: Read

**Organization permissions:**
- **Members**: Read & Write
- **Administration**: Read

#### Step 3: Install the App

1. After creating the app, go to the **Install App** tab
2. Install the app in your organization
3. Grant access to all repositories or select specific ones

#### Step 4: Get Configuration Values

1. **App ID**: Found in the app settings page
2. **Installation ID**: 
   - Go to your organization settings
   - Navigate to **Installed GitHub Apps**
   - Click **Configure** next to your app
   - The installation ID is in the URL: `https://github.com/organizations/ORG/settings/installations/INSTALLATION_ID`
3. **Private Key**:
   - In the app settings, scroll down to **Private keys**
   - Click **Generate a private key**
   - Download the `.pem` file

#### Step 5: Configure Environment

```bash
# GitHub App Configuration
GITHUB__AUTH_METHOD=github_app
GITHUB__APP_ID=123456
GITHUB__INSTALLATION_ID=12345678
GITHUB__PRIVATE_KEY_PATH=/path/to/your/private-key.pem
GITHUB__ORG_NAME=your-organization-name
```

### Option 2: Personal Access Token

#### Step 1: Generate Token

1. Go to GitHub **Settings** > **Developer settings** > **Personal access tokens**
2. Click **Generate new token (classic)**
3. Set an appropriate expiration (90 days recommended)
4. Select the following scopes:

**Required Scopes:**
- `repo` - Full control of private repositories (or `public_repo` for public only)
- `read:org` - Read org and team membership
- `admin:org` - Fully manage the organization and its teams

#### Step 2: Configure Environment

```bash
# Personal Access Token Configuration
GITHUB__AUTH_METHOD=personal_access_token
GITHUB__PERSONAL_ACCESS_TOKEN=ghp_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
GITHUB__ORG_NAME=your-organization-name
```

## Environment Configuration

### Required Variables

| Variable | Description | Required For |
|----------|-------------|--------------|
| `GITHUB__AUTH_METHOD` | Authentication method (`github_app` or `personal_access_token`) | Both |
| `GITHUB__ORG_NAME` | GitHub organization name | Both |
| `GITHUB__APP_ID` | GitHub App ID | GitHub App |
| `GITHUB__INSTALLATION_ID` | GitHub App installation ID | GitHub App |
| `GITHUB__PRIVATE_KEY_PATH` | Path to private key file | GitHub App |
| `GITHUB__PRIVATE_KEY` | Private key content (alternative to file path) | GitHub App |
| `GITHUB__PERSONAL_ACCESS_TOKEN` | Personal access token | PAT |

### Optional Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `GITHUB__API_BASE_URL` | `https://api.github.com` | GitHub API base URL |
| `GITHUB__API_TIMEOUT` | `30` | API request timeout (seconds) |
| `GITHUB__API_RETRY_ATTEMPTS` | `3` | Number of retry attempts |

## Testing the Integration

### Method 1: Test Script

Run the provided test script to verify your configuration:

```bash
python scripts/test_github_integration.py
```

This script will:
- ✅ Validate environment configuration
- ✅ Test GitHub API connection
- ✅ List repositories, users, and teams
- ✅ Test repository team access

### Method 2: API Endpoint

Start the application and test the connection endpoint:

```bash
# Start the application
uvicorn app.main:app --reload

# Test the connection (requires authentication)
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     http://localhost:8000/v1/github/test-connection
```

### Expected Test Output

```
🚀 GitHub Integration Test
==================================================
🔧 Checking environment configuration...
   Authentication method: github_app
   Organization: your-org
✅ GitHub App configuration looks good

🔗 Testing GitHub API connection...
✅ Connection successful!
   Authentication method: github_app
   Organization: your-org
   Rate limit remaining: 4999

📁 Testing repository listing...
✅ Found 25 repositories (showing first 5)
   - frontend-app (private)
   - backend-api (private)
   - mobile-app (private)
   ... and 22 more

👥 Testing user listing...
✅ Found 15 users (showing first 5)
   - john.doe (John Doe)
   - jane.smith (Jane Smith)
   - bob.wilson (Bob Wilson)
   ... and 12 more

🏢 Testing team listing...
✅ Found 8 teams (showing first 5)
   - frontend-team (5 members)
   - backend-team (8 members)
   - devops-team (3 members)
   ... and 5 more

🔐 Testing repository team access...
✅ Repository 'frontend-app' has 2 team(s) with access
   - frontend-team (maintain access)
   - devops-team (admin access)

==================================================
📊 Test Summary
✅ All 4 tests passed!

🎉 GitHub integration is working correctly!
```

## Troubleshooting

### Common Issues

#### 1. Authentication Failed

**Error**: `GitHub connection test failed: Authentication failed`

**Solutions**:
- Verify your App ID and Installation ID are correct
- Check that the private key file exists and is readable
- Ensure the GitHub App is installed in your organization
- Verify the Personal Access Token is valid and has required scopes

#### 2. Permission Denied

**Error**: `Insufficient permissions: ...`

**Solutions**:
- Check GitHub App permissions (Administration, Members, etc.)
- Verify Personal Access Token scopes (`repo`, `read:org`, `admin:org`)
- Ensure the user/app has access to the organization

#### 3. Rate Limit Exceeded

**Error**: `Rate limit exceeded`

**Solutions**:
- Wait for the rate limit to reset
- Consider using GitHub App instead of PAT for higher limits
- Implement request throttling in your application

#### 4. Organization Not Found

**Error**: `Resource not found: /orgs/your-org`

**Solutions**:
- Verify the organization name is correct
- Check that the user/app has access to the organization
- Ensure the organization exists and is accessible

### Getting Help

If you encounter issues:

1. **Check the logs** for detailed error messages
2. **Run the test script** to identify specific problems
3. **Verify permissions** in GitHub settings
4. **Check rate limits** using the test connection endpoint

### Security Best Practices

#### For GitHub Apps
- Store private keys securely (encrypted, restricted access)
- Regularly rotate private keys
- Monitor app installation and permissions
- Use minimal required permissions

#### For Personal Access Tokens
- Use minimal required scopes
- Rotate tokens every 90 days maximum
- Store tokens in secure environment variables
- Never commit tokens to version control
- Monitor token usage and access patterns

## Next Steps

Once your GitHub integration is working:

1. **Explore the API endpoints** in the documentation
2. **Set up the frontend** to use the GitHub management features
3. **Configure team and repository management** workflows
4. **Set up monitoring** for API usage and rate limits

For detailed API documentation, see the [API Specification](./API_SPECIFICATION.md).
