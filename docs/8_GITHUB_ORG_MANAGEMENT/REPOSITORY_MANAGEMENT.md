# Repository Management Feature

This document details the repository management functionality that allows users to view and manage GitHub organization repositories and their team assignments.

## Overview

The repository management feature provides a comprehensive interface for:
- Viewing all repositories in the GitHub organization
- Managing team assignments for individual repositories
- Performing bulk team assignments across multiple repositories
- Managing GitHub repository permissions through team roles

## User Interface Design

### Repository List View

The main repository list displays all repositories in the organization with the following information:

#### Repository List Columns
| Column | Description | Actions |
|--------|-------------|---------|
| **☑️ Checkbox** | Multi-select for bulk operations | Select/deselect repository |
| **📁 Repository Name** | Repository name (clickable) | Click to open repository details |
| **📝 Description** | Repository description | Display only |
| **🔒 Visibility** | Public/Private status | Display with icon |
| **👥 Teams** | Number of assigned teams | Display count with tooltip |
| **📅 Last Updated** | Last modification date | Display formatted date |
| **⚙️ Actions** | Individual actions | Manage Teams button |

#### Repository List Features
- **Pagination**: Handle large numbers of repositories
- **Search**: Filter repositories by name or description
- **Sorting**: Sort by name, last updated, visibility
- **Filtering**: Filter by visibility (public/private), team assignments
- **Bulk Selection**: "Select All" checkbox for bulk operations

### Individual Repository Management

When clicking on a repository name, users can view and manage team assignments:

#### Repository Details Modal/Page
```
┌─────────────────────────────────────────────────────────────┐
│ 📁 Repository: example-repo                                 │
├─────────────────────────────────────────────────────────────┤
│ Description: Example repository for demonstration           │
│ Visibility: 🔒 Private                                      │
│ URL: https://github.com/org/example-repo                   │
├─────────────────────────────────────────────────────────────┤
│ Team Assignments:                                           │
│                                                             │
│ ┌─────────────────┬──────────────┬─────────────────────────┐ │
│ │ Team Name       │ Role         │ Actions                 │ │
│ ├─────────────────┼──────────────┼─────────────────────────┤ │
│ │ 👥 Frontend     │ 🔧 Maintain  │ [Edit] [Remove]        │ │
│ │ 👥 Backend      │ ✏️ Write     │ [Edit] [Remove]        │ │
│ │ 👥 DevOps       │ 👑 Admin     │ [Edit] [Remove]        │ │
│ └─────────────────┴──────────────┴─────────────────────────┘ │
│                                                             │
│ [+ Add Team]                                               │
├─────────────────────────────────────────────────────────────┤
│                                    [Cancel] [Save Changes] │
└─────────────────────────────────────────────────────────────┘
```

#### Add Team Assignment Dialog
```
┌─────────────────────────────────────────────────────────────┐
│ Add Team to Repository: example-repo                        │
├─────────────────────────────────────────────────────────────┤
│ Select Team:                                                │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ [Dropdown: Select Team...]                              │ │
│ │ ▼ Frontend Team                                         │ │
│ │   Backend Team                                          │ │
│ │   DevOps Team                                           │ │
│ │   QA Team                                               │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ Select Role:                                                │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ ○ Read    - Can view and clone repository               │ │
│ │ ○ Write   - Can push to repository                      │ │
│ │ ● Maintain - Can manage repository settings             │ │
│ │ ○ Admin   - Full administrative access                  │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                                        [Cancel] [Add Team] │
└─────────────────────────────────────────────────────────────┘
```

### Bulk Repository Team Assignment

When multiple repositories are selected, users can perform bulk team assignments:

#### Bulk Assignment Dialog
```
┌─────────────────────────────────────────────────────────────┐
│ Bulk Team Assignment                                        │
├─────────────────────────────────────────────────────────────┤
│ Selected Repositories (3):                                  │
│ • example-repo                                              │
│ • another-repo                                              │
│ • third-repo                                                │
├─────────────────────────────────────────────────────────────┤
│ Team Assignments:                                           │
│                                                             │
│ Assignment 1:                                               │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Team: [Dropdown: Frontend Team        ▼]               │ │
│ │ Role: [Dropdown: Maintain             ▼]               │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ Assignment 2:                                               │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Team: [Dropdown: Backend Team         ▼]               │ │
│ │ Role: [Dropdown: Write                ▼]               │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ [+ Add Another Team Assignment]                             │
├─────────────────────────────────────────────────────────────┤
│ Options:                                                    │
│ ☑️ Replace existing team assignments                        │
│ ☐ Add to existing team assignments                         │
├─────────────────────────────────────────────────────────────┤
│                                [Cancel] [Assign Teams]     │
└─────────────────────────────────────────────────────────────┘
```

## GitHub Roles and Permissions

### Repository Permission Levels

| Role | GitHub Permission | Capabilities |
|------|------------------|--------------|
| **Read** | `pull` | • View repository content<br>• Clone repository<br>• Download repository<br>• View issues and pull requests |
| **Write** | `push` | • All Read permissions<br>• Push to repository<br>• Create issues and pull requests<br>• Edit repository wiki |
| **Maintain** | `maintain` | • All Write permissions<br>• Manage repository settings<br>• Manage pull request reviews<br>• Edit repository topics |
| **Admin** | `admin` | • All Maintain permissions<br>• Full repository administration<br>• Manage team access<br>• Delete repository |

**Note**: GitHub API uses `pull`, `push`, `maintain`, and `admin` as permission values. The legacy `triage` permission is also available but not commonly used.

### Role Selection Guidelines

- **Read**: For teams that need to view code but not make changes
- **Write**: For development teams that need to push code
- **Maintain**: For senior developers and team leads who manage PRs
- **Admin**: For repository owners and administrators only

## API Endpoints

### Repository Management APIs

#### List Organization Repositories
```http
GET /v1/github/repositories
```

**Query Parameters:**
- `page` (optional): Page number for pagination
- `per_page` (optional): Number of repositories per page (default: 30)
- `search` (optional): Search term for repository name/description
- `visibility` (optional): Filter by visibility (public, private, all)
- `sort` (optional): Sort by (name, updated, created)
- `order` (optional): Sort order (asc, desc)

**Response:**
```json
{
  "success": true,
  "data": {
    "repositories": [
      {
        "id": 123456789,
        "name": "example-repo",
        "full_name": "organization/example-repo",
        "description": "Example repository for demonstration",
        "private": true,
        "html_url": "https://github.com/organization/example-repo",
        "updated_at": "2024-01-15T10:30:00Z",
        "teams_count": 3,
        "language": "JavaScript",
        "default_branch": "main"
      }
    ],
    "pagination": {
      "page": 1,
      "per_page": 30,
      "total": 150,
      "total_pages": 5
    }
  },
  "error": null
}
```

#### Get Repository Team Assignments
```http
GET /v1/github/repositories/{repo_id}/teams
```

**Response:**
```json
{
  "success": true,
  "data": {
    "repository": {
      "id": 123456789,
      "name": "example-repo",
      "full_name": "organization/example-repo"
    },
    "teams": [
      {
        "id": 987654321,
        "name": "frontend-team",
        "slug": "frontend-team",
        "permission": "maintain",
        "members_count": 5
      }
    ]
  },
  "error": null
}
```

#### Bulk Assign Teams to Repositories
```http
POST /v1/github/repositories/teams/assign
```

**Request Body:**
```json
{
  "repository_names": ["repo1", "repo2", "repo3"],
  "team_assignments": [
    {
      "team_slug": "frontend-team",
      "permission": "maintain"
    },
    {
      "team_slug": "backend-team",
      "permission": "push"
    }
  ],
  "replace_existing": true
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "assignments_created": 6,
    "assignments_updated": 0,
    "assignments_failed": 0,
    "results": [
      {
        "repository_id": 123456789,
        "team_id": 111111111,
        "status": "success",
        "permission": "maintain"
      }
    ]
  },
  "error": null
}
```

## Implementation Details

### Frontend Components

#### RepositoryList Component
- **State Management**: Repository list, selected repositories, loading states
- **Pagination**: Handle large repository lists efficiently
- **Search/Filter**: Real-time filtering and search functionality
- **Bulk Selection**: Multi-select with "select all" functionality

#### RepositoryDetails Component
- **Team Display**: Show current team assignments with roles
- **Team Management**: Add/remove/edit team assignments
- **Role Selection**: Dropdown with role descriptions
- **Save Changes**: Batch updates for efficiency

#### BulkTeamAssignment Component
- **Repository Selection**: Display selected repositories
- **Team Assignment**: Multiple team/role combinations
- **Assignment Options**: Replace vs. add to existing assignments
- **Progress Tracking**: Show assignment progress for large operations

### Backend Services

#### GitHubRepositoryService
- **Repository Listing**: Fetch repositories with pagination
- **Team Assignments**: Get/set repository team permissions
- **Bulk Operations**: Efficient batch processing
- **Error Handling**: Graceful handling of GitHub API errors

#### GitHubTeamService
- **Team Listing**: Fetch organization teams
- **Permission Management**: Handle GitHub permission levels
- **Validation**: Ensure valid team/permission combinations

### Error Handling

#### Common Error Scenarios
- **Rate Limiting**: Handle GitHub API rate limits gracefully
- **Permission Errors**: Clear messaging for insufficient permissions
- **Network Errors**: Retry logic and user feedback
- **Validation Errors**: Client-side and server-side validation

#### User Feedback
- **Loading States**: Show progress during API operations
- **Success Messages**: Confirm successful operations
- **Error Messages**: Clear, actionable error descriptions
- **Bulk Operation Progress**: Progress bars for large operations
