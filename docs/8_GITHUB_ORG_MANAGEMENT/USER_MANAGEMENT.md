# User Management Feature

This document details the user management functionality that allows administrators to view and manage GitHub organization users and their team memberships.

## Overview

The user management feature provides a comprehensive interface for:
- Viewing all users in the GitHub organization
- Managing team memberships for individual users
- Performing bulk team assignments across multiple users
- Managing GitHub organization permissions through team memberships

## User Interface Design

### User List View

The main user list displays all users in the organization with the following information:

#### User List Columns
| Column | Description | Actions |
|--------|-------------|---------|
| **☑️ Checkbox** | Multi-select for bulk operations | Select/deselect user |
| **👤 Username** | GitHub username (clickable) | Click to open user details |
| **📧 Email** | User email address | Display only |
| **👥 Teams** | Number of team memberships | Display count with tooltip |
| **🔑 Role** | Organization role (Member/Admin) | Display with icon |
| **📅 Joined** | Date joined organization | Display formatted date |
| **⚙️ Actions** | Individual actions | Manage Teams button |

#### User List Features
- **Pagination**: Handle large numbers of users
- **Search**: Filter users by username or email
- **Sorting**: Sort by username, email, join date, team count
- **Filtering**: Filter by organization role, team membership
- **Bulk Selection**: "Select All" checkbox for bulk operations

### Individual User Management

When clicking on a username, administrators can view and manage team memberships:

#### User Details Modal/Page
```
┌─────────────────────────────────────────────────────────────┐
│ 👤 User: john.doe                                           │
├─────────────────────────────────────────────────────────────┤
│ Email: <EMAIL>                                 │
│ Organization Role: 👤 Member                                │
│ Joined: January 15, 2024                                   │
│ Profile: https://github.com/john.doe                       │
├─────────────────────────────────────────────────────────────┤
│ Team Memberships:                                           │
│                                                             │
│ ┌─────────────────┬──────────────┬─────────────────────────┐ │
│ │ Team Name       │ Role         │ Actions                 │ │
│ ├─────────────────┼──────────────┼─────────────────────────┤ │
│ │ 👥 Frontend     │ 👤 Member    │ [Edit] [Remove]        │ │
│ │ 👥 Backend      │ 👑 Maintainer│ [Edit] [Remove]        │ │
│ │ 👥 DevOps       │ 👤 Member    │ [Edit] [Remove]        │ │
│ └─────────────────┴──────────────┴─────────────────────────┘ │
│                                                             │
│ [+ Add to Team]                                            │
├─────────────────────────────────────────────────────────────┤
│                                    [Cancel] [Save Changes] │
└─────────────────────────────────────────────────────────────┘
```

#### Add User to Teams Dialog
```
┌─────────────────────────────────────────────────────────────┐
│ Add User to Teams: john.doe                                 │
├─────────────────────────────────────────────────────────────┤
│ Select Teams:                                               │
│                                                             │
│ Team Selection 1:                                           │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ [Dropdown: Select Team...]                              │ │
│ │ ▼ Frontend Team                                         │ │
│ │   Backend Team                                          │ │
│ │   DevOps Team                                           │ │
│ │   QA Team                                               │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ Team Selection 2:                                           │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ [Dropdown: Select Team...]                              │ │
│ │ ▼ Backend Team                                          │ │
│ │   Frontend Team                                         │ │
│ │   DevOps Team                                           │ │
│ │   QA Team                                               │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ [+ Add Another Team]                                        │
├─────────────────────────────────────────────────────────────┤
│                                    [Cancel] [Add to Teams] │
└─────────────────────────────────────────────────────────────┘
```

### Bulk User Team Assignment

When multiple users are selected, administrators can perform bulk team assignments:

#### Bulk Assignment Dialog
```
┌─────────────────────────────────────────────────────────────┐
│ Bulk Team Assignment                                        │
├─────────────────────────────────────────────────────────────┤
│ Selected Users (5):                                         │
│ • john.doe                                                  │
│ • jane.smith                                                │
│ • bob.wilson                                                │
│ • alice.johnson                                             │
│ • mike.brown                                                │
├─────────────────────────────────────────────────────────────┤
│ Team Assignments:                                           │
│                                                             │
│ Team Selection 1:                                           │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Team: [Dropdown: Frontend Team        ▼]               │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ Team Selection 2:                                           │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Team: [Dropdown: Backend Team         ▼]               │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ Team Selection 3:                                           │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Team: [Dropdown: DevOps Team          ▼]               │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ [+ Add Another Team]                                        │
├─────────────────────────────────────────────────────────────┤
│ Options:                                                    │
│ ☑️ Replace existing team memberships                        │
│ ☐ Add to existing team memberships                         │
│ ☐ Send notification emails to users                        │
├─────────────────────────────────────────────────────────────┤
│                                [Cancel] [Assign to Teams]  │
└─────────────────────────────────────────────────────────────┘
```

## Team Assignment Overview

### Team Membership

Users are simply assigned to teams without specific role management. The focus is on team membership rather than individual user permissions:

- **Team Assignment**: Users are added as members to selected teams
- **Multiple Teams**: Users can be assigned to multiple teams simultaneously
- **Team Access**: Users inherit access to repositories based on team permissions
- **Automatic Updates**: Changes to team repository access automatically apply to all team members

### Team Permission Inheritance

When users are added to teams, they inherit the team's repository permissions:
- **Team Repository Access**: Users get access to all repositories assigned to the team
- **Permission Level**: Users inherit the permission level assigned to the team for each repository
- **No Individual Permissions**: User permissions are managed at the team level, not individually

## API Endpoints

### User Management APIs

#### List Organization Users
```http
GET /v1/github/users
```

**Query Parameters:**
- `page` (optional): Page number for pagination
- `per_page` (optional): Number of users per page (default: 30, max: 100)
- `search` (optional): Search term for username (optimized - only searches usernames)
- `role` (optional): Filter by organization role (member, admin, all)

**Performance Note:** This endpoint is optimized for speed and only makes one API call to GitHub. User details like email and full name are not included. Use the `/users/{username}/details` endpoint when you need complete user information.

**Response:**
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": 123456789,
        "login": "john.doe",
        "email": "<EMAIL>",
        "name": "John Doe",
        "avatar_url": "https://avatars.githubusercontent.com/u/123456789",
        "html_url": "https://github.com/john.doe",
        "role": "member",
        "teams_count": 3,
        "created_at": "2024-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "per_page": 30,
      "total": 75,
      "total_pages": 3
    }
  },
  "error": null
}
```

#### Get User Team Memberships
```http
GET /v1/github/users/{user_id}/teams
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 123456789,
      "login": "john.doe",
      "email": "<EMAIL>",
      "name": "John Doe"
    },
    "teams": [
      {
        "id": 987654321,
        "name": "frontend-team",
        "slug": "frontend-team",
        "description": "Frontend development team",
        "repositories_count": 5
      }
    ]
  },
  "error": null
}
```

#### Bulk Assign Users to Teams
```http
POST /v1/github/users/teams/assign
```

**Request Body:**
```json
{
  "usernames": ["john.doe", "jane.smith", "bob.wilson"],
  "team_slugs": ["frontend-team", "backend-team", "devops-team"],
  "role": "member",
  "replace_existing": false
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "assignments_created": 6,
    "assignments_updated": 0,
    "assignments_failed": 0,
    "notifications_sent": 3,
    "results": [
      {
        "user_id": 123456789,
        "username": "john.doe",
        "team_id": 111111111,
        "team_name": "Frontend Team",
        "status": "success"
      }
    ]
  },
  "error": null
}
```

#### Add User to Team
```http
PUT /v1/github/teams/{team_slug}/memberships/{username}
```

**Request Body:**
```json
{
  "role": "member"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "url": "https://api.github.com/teams/1/memberships/john.doe",
    "role": "member",
    "state": "active"
  },
  "error": null
}
```

#### Remove User from Team
```http
DELETE /v1/github/teams/{team_slug}/memberships/{username}
```

## Implementation Details

### Frontend Components

#### UserList Component
- **State Management**: User list, selected users, loading states
- **Pagination**: Handle large user lists efficiently
- **Search/Filter**: Real-time filtering and search functionality
- **Bulk Selection**: Multi-select with "select all" functionality

#### UserDetails Component
- **Team Display**: Show current team memberships with roles
- **Team Management**: Add/remove/edit team memberships
- **Role Selection**: Dropdown with role descriptions
- **Save Changes**: Batch updates for efficiency

#### BulkTeamAssignment Component
- **User Selection**: Display selected users
- **Team Assignment**: Multiple team/role combinations
- **Assignment Options**: Replace vs. add to existing memberships
- **Notification Options**: Send email notifications to users
- **Progress Tracking**: Show assignment progress for large operations

### Backend Services

#### GitHubUserService
- **User Listing**: Fetch organization users with pagination
- **Team Memberships**: Get/set user team memberships
- **Bulk Operations**: Efficient batch processing
- **Error Handling**: Graceful handling of GitHub API errors

#### GitHubTeamService
- **Team Listing**: Fetch organization teams
- **Member Management**: Add/remove team members
- **Role Management**: Handle team member roles
- **Validation**: Ensure valid user/team combinations

### Security Considerations

#### Permission Requirements
- **Organization Admin**: Required for user management operations
- **Team Maintainer**: Can manage members of specific teams only
- **Audit Logging**: Log all user management operations
- **Rate Limiting**: Respect GitHub API rate limits

#### Data Privacy
- **Email Visibility**: Respect user privacy settings
- **Profile Information**: Only show publicly available information
- **Team Visibility**: Respect team privacy settings

### Error Handling

#### Common Error Scenarios
- **Permission Denied**: User lacks required permissions
- **User Not Found**: User no longer exists in organization
- **Team Not Found**: Team has been deleted
- **Rate Limiting**: Handle GitHub API rate limits gracefully
- **Network Errors**: Retry logic and user feedback

#### User Feedback
- **Loading States**: Show progress during API operations
- **Success Messages**: Confirm successful operations
- **Error Messages**: Clear, actionable error descriptions
- **Bulk Operation Progress**: Progress bars for large operations
- **Notification Status**: Confirm email notifications sent

### Performance Optimization

#### Caching Strategy
- **User List Caching**: Cache user lists with TTL
- **Team Data Caching**: Cache team information
- **Pagination Optimization**: Efficient pagination handling
- **Bulk Operation Batching**: Process large operations in batches

#### API Optimization
- **Parallel Requests**: Execute independent API calls in parallel
- **Request Batching**: Combine related API calls where possible
- **Error Recovery**: Graceful handling of partial failures
- **Progress Reporting**: Real-time progress updates for bulk operations
