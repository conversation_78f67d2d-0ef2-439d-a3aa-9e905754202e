# GitHub Organization Management API Specification

This document provides the complete API specification for the GitHub organization management feature.

## Base URL and Authentication

**Base URL**: `/v1/github`

**Authentication**: All endpoints require JWT token authentication via Authorization header:
```
Authorization: Bearer <jwt_token>
```

## Repository Management APIs

### List Organization Repositories

**Endpoint**: `GET /v1/github/repositories`

**Description**: Retrieve a paginated list of all repositories in the organization.

**Query Parameters**:
- `page` (integer, optional): Page number (default: 1)
- `per_page` (integer, optional): Items per page (default: 30, max: 100)
- `search` (string, optional): Search term for repository name or description
- `visibility` (string, optional): Filter by visibility (`public`, `private`, `all`)
- `sort` (string, optional): Sort by (`name`, `updated`, `created`)
- `order` (string, optional): Sort order (`asc`, `desc`)

**Response**:
```json
{
  "success": true,
  "data": {
    "repositories": [
      {
        "id": 123456789,
        "name": "example-repo",
        "full_name": "organization/example-repo",
        "description": "Example repository description",
        "private": true,
        "html_url": "https://github.com/organization/example-repo",
        "clone_url": "https://github.com/organization/example-repo.git",
        "ssh_url": "**************:organization/example-repo.git",
        "language": "JavaScript",
        "default_branch": "main",
        "teams_count": 3,
        "updated_at": "2024-01-15T10:30:00Z",
        "created_at": "2024-01-01T09:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "per_page": 30,
      "total": 150,
      "total_pages": 5,
      "has_next": true,
      "has_prev": false
    }
  },
  "error": null
}
```

### Get Repository Team Assignments

**Endpoint**: `GET /v1/github/repositories/{repo_id}/teams`

**Description**: Get all teams assigned to a specific repository with their permission levels.

**Path Parameters**:
- `repo_id` (integer): GitHub repository ID

**Response**:
```json
{
  "success": true,
  "data": {
    "repository": {
      "id": 123456789,
      "name": "example-repo",
      "full_name": "organization/example-repo",
      "private": true
    },
    "teams": [
      {
        "id": 987654321,
        "name": "Frontend Team",
        "slug": "frontend-team",
        "description": "Frontend development team",
        "permission": "maintain",
        "members_count": 8,
        "repositories_count": 12
      }
    ]
  },
  "error": null
}
```

### Bulk Assign Teams to Repositories

**Endpoint**: `POST /v1/github/repositories/teams/assign`

**Description**: Assign multiple teams to multiple repositories with specified permissions.

**Request Body**:
```json
{
  "repository_names": ["repo1", "repo2", "repo3"],
  "team_assignments": [
    {
      "team_slug": "frontend-team",
      "permission": "maintain"
    },
    {
      "team_slug": "backend-team",
      "permission": "push"
    }
  ],
  "replace_existing": true
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "assignments_created": 6,
    "assignments_updated": 0,
    "assignments_failed": 0,
    "results": [
      {
        "repository_id": 123456789,
        "repository_name": "example-repo",
        "team_id": 111111111,
        "team_name": "Frontend Team",
        "status": "success",
        "permission": "maintain"
      }
    ]
  },
  "error": null
}
```

### Add or Update Team Repository Permission

**Endpoint**: `PUT /v1/github/teams/{team_slug}/repos/{owner}/{repo}`

**Description**: Add a team to a repository or update the team's permission level. This endpoint follows GitHub's official API specification.

**Path Parameters**:
- `team_slug` (string): GitHub team slug (e.g., "frontend-team")
- `owner` (string): Repository owner (organization name)
- `repo` (string): Repository name

**Request Body**:
```json
{
  "permission": "maintain"
}
```

**Permission Values**:
- `pull` - Read access
- `push` - Write access
- `maintain` - Maintain access
- `admin` - Admin access

**Response**:
```json
{
  "success": true,
  "data": {
    "team_slug": "frontend-team",
    "repository": "owner/repo",
    "permission": "maintain",
    "updated_at": "2024-01-15T10:30:00Z"
  },
  "error": null
}
```

### Remove Team from Repository

**Endpoint**: `DELETE /v1/github/teams/{team_slug}/repos/{owner}/{repo}`

**Description**: Remove a team's access to a specific repository.

**Path Parameters**:
- `team_slug` (string): GitHub team slug
- `owner` (string): Repository owner (organization name)
- `repo` (string): Repository name

**Response**:
```json
{
  "success": true,
  "data": {
    "repository_id": 123456789,
    "team_id": 111111111,
    "removed_at": "2024-01-15T10:30:00Z"
  },
  "error": null
}
```

## User Management APIs

### List Organization Users

**Endpoint**: `GET /v1/github/users`

**Description**: Retrieve a paginated list of all users in the organization.

**Query Parameters**:
- `page` (integer, optional): Page number (default: 1)
- `per_page` (integer, optional): Items per page (default: 30, max: 100)
- `search` (string, optional): Search term for username or email
- `role` (string, optional): Filter by organization role (`member`, `admin`, `all`)
- `team_id` (integer, optional): Filter by team membership
- `sort` (string, optional): Sort by (`username`, `email`, `joined`)
- `order` (string, optional): Sort order (`asc`, `desc`)

**Response**:
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": 123456789,
        "login": "john.doe",
        "email": "<EMAIL>",
        "name": "John Doe",
        "avatar_url": "https://avatars.githubusercontent.com/u/123456789",
        "html_url": "https://github.com/john.doe",
        "role": "member",
        "teams_count": 3,
        "created_at": "2024-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "per_page": 30,
      "total": 75,
      "total_pages": 3,
      "has_next": true,
      "has_prev": false
    }
  },
  "error": null
}
```

### Get User Team Memberships

**Endpoint**: `GET /v1/github/users/{user_id}/teams`

**Description**: Get all teams that a specific user belongs to in the organization.

**Path Parameters**:
- `user_id` (integer): GitHub user ID

**Response**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 123456789,
      "login": "john.doe",
      "email": "<EMAIL>",
      "name": "John Doe",
      "role": "member"
    },
    "teams": [
      {
        "id": 987654321,
        "name": "Frontend Team",
        "slug": "frontend-team",
        "description": "Frontend development team",
        "repositories_count": 12
      }
    ]
  },
  "error": null
}
```

### Bulk Assign Users to Teams

**Endpoint**: `POST /v1/github/users/teams/assign`

**Description**: Assign multiple users to multiple teams.

**Request Body**:
```json
{
  "usernames": ["john.doe", "jane.smith", "bob.wilson"],
  "team_slugs": ["frontend-team", "backend-team", "devops-team"],
  "role": "member",
  "replace_existing": false
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "assignments_created": 6,
    "assignments_updated": 0,
    "assignments_failed": 0,
    "notifications_sent": 3,
    "results": [
      {
        "user_id": 123456789,
        "username": "john.doe",
        "team_id": 111111111,
        "team_name": "Frontend Team",
        "status": "success"
      }
    ]
  },
  "error": null
}
```

## Team Management APIs

### List Organization Teams

**Endpoint**: `GET /v1/github/teams`

**Description**: Retrieve a paginated list of all teams in the organization.

**Query Parameters**:
- `page` (integer, optional): Page number (default: 1)
- `per_page` (integer, optional): Items per page (default: 30, max: 100)
- `search` (string, optional): Search term for team name or description

**Response**:
```json
{
  "success": true,
  "data": {
    "teams": [
      {
        "id": 987654321,
        "name": "Frontend Team",
        "slug": "frontend-team",
        "description": "Frontend development team",
        "privacy": "closed",
        "members_count": 8,
        "repositories_count": 12,
        "created_at": "2024-01-01T09:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "per_page": 30,
      "total": 25,
      "total_pages": 1,
      "has_next": false,
      "has_prev": false
    }
  },
  "error": null
}
```

### Get Team Members

**Endpoint**: `GET /v1/github/teams/{team_id}/members`

**Description**: Get all members of a specific team.

**Path Parameters**:
- `team_id` (integer): GitHub team ID

**Response**:
```json
{
  "success": true,
  "data": {
    "team": {
      "id": 987654321,
      "name": "Frontend Team",
      "slug": "frontend-team"
    },
    "members": [
      {
        "id": 123456789,
        "login": "john.doe",
        "name": "John Doe",
        "email": "<EMAIL>",
        "role": "member",
        "avatar_url": "https://avatars.githubusercontent.com/u/123456789"
      }
    ]
  },
  "error": null
}
```

### Get Team Repositories

**Endpoint**: `GET /v1/github/teams/{team_id}/repositories`

**Description**: Get all repositories accessible to a specific team.

**Path Parameters**:
- `team_id` (integer): GitHub team ID

**Response**:
```json
{
  "success": true,
  "data": {
    "team": {
      "id": 987654321,
      "name": "Frontend Team",
      "slug": "frontend-team"
    },
    "repositories": [
      {
        "id": 123456789,
        "name": "example-repo",
        "full_name": "organization/example-repo",
        "permission": "maintain",
        "private": true
      }
    ]
  },
  "error": null
}
```

## Error Responses

### Standard Error Format

All error responses follow this format:

```json
{
  "success": false,
  "data": null,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": {}
  }
}
```

### Common Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `AUTHENTICATION_REQUIRED` | 401 | Missing or invalid authentication token |
| `PERMISSION_DENIED` | 403 | Insufficient permissions for the operation |
| `RESOURCE_NOT_FOUND` | 404 | Requested resource does not exist |
| `VALIDATION_ERROR` | 422 | Request validation failed |
| `RATE_LIMIT_EXCEEDED` | 429 | GitHub API rate limit exceeded |
| `GITHUB_API_ERROR` | 502 | Error from GitHub API |
| `INTERNAL_SERVER_ERROR` | 500 | Unexpected server error |

### Example Error Responses

#### Validation Error
```json
{
  "success": false,
  "data": null,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Request validation failed",
    "details": {
      "repository_ids": ["At least one repository ID is required"],
      "team_assignments": ["At least one team assignment is required"]
    }
  }
}
```

#### Rate Limit Exceeded
```json
{
  "success": false,
  "data": null,
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "GitHub API rate limit exceeded",
    "details": {
      "reset_time": "2024-01-15T11:00:00Z",
      "remaining": 0,
      "limit": 5000
    }
  }
}
```

## Permission Levels

### Repository Permissions

| Permission | Description |
|------------|-------------|
| `pull` | Read access - can view and clone repository |
| `push` | Write access - can push to repository |
| `maintain` | Maintain access - can manage repository settings |
| `admin` | Admin access - full repository administration |

### Team Roles

| Role | Description |
|------|-------------|
| `member` | Standard team member |
| `maintainer` | Team maintainer - can manage team settings and members |

## Rate Limiting

- **GitHub API Limits**: 5,000 requests per hour per installation
- **Bulk Operations**: Automatically batched to respect rate limits
- **Rate Limit Headers**: Included in all responses
- **Retry Logic**: Automatic retry with exponential backoff

## Pagination

All list endpoints support pagination with the following parameters:
- `page`: Page number (1-based)
- `per_page`: Items per page (max 100)

Pagination information is included in the response:
```json
{
  "pagination": {
    "page": 1,
    "per_page": 30,
    "total": 150,
    "total_pages": 5,
    "has_next": true,
    "has_prev": false
  }
}
```
