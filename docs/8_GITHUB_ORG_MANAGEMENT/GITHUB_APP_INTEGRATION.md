# GitHub API Integration

This document details the GitHub API integration for the organization management feature, supporting both GitHub App and Personal Access Token authentication methods.

## Overview

The GitHub API integration provides secure access to GitHub's API for managing organization repositories, teams, and users. Two authentication methods are supported:

### Authentication Options

#### Option 1: GitHub App (Recommended)
- **Better Security**: App-level permissions with installation tokens
- **Higher Rate Limits**: 5,000 requests per hour per installation
- **Granular Permissions**: Fine-grained access control
- **Organization-wide**: Centralized management for organizations

#### Option 2: Personal Access Token (PAT)
- **Simpler Setup**: Direct token-based authentication
- **User-based**: Permissions based on user's access level
- **Quick Development**: Faster to set up for development/testing
- **Rate Limits**: 5,000 requests per hour per user

## GitHub App Configuration

### Required Permissions

The GitHub App must be configured with the following permissions:

#### Repository Permissions
- **Administration**: Read & Write
  - Required for managing repository team access
  - Needed for repository settings management
- **Metadata**: Read
  - Required for accessing repository information
  - Needed for repository listing

#### Organization Permissions
- **Members**: Read & Write
  - Required for listing organization users
  - Needed for managing team memberships
- **Administration**: Read
  - Required for accessing organization information
  - Needed for team management

### Installation Requirements

The GitHub App must be installed at the organization level with access to:
- All repositories (or specific repositories as needed)
- Organization member information
- Team management capabilities

## Authentication Flow

### GitHub App Authentication Process

```mermaid
sequenceDiagram
    participant Backend
    participant GitHub
    participant User

    Backend->>GitHub: Generate JWT with App ID + Private Key
    GitHub-->>Backend: JWT Token
    Backend->>GitHub: Get Installation Access Token
    GitHub-->>Backend: Installation Token (1 hour TTL)
    Backend->>GitHub: API Request with Installation Token
    GitHub-->>Backend: API Response
    Backend-->>User: Processed Data
```

### Token Management

#### JWT Token Generation
```python
import jwt
import time
from datetime import datetime, timedelta

def generate_jwt_token(app_id: str, private_key: str) -> str:
    """Generate JWT token for GitHub App authentication."""
    now = int(time.time())
    payload = {
        'iat': now,
        'exp': now + (10 * 60),  # 10 minutes
        'iss': app_id
    }
    return jwt.encode(payload, private_key, algorithm='RS256')
```

#### Installation Token Management
```python
async def get_installation_token(installation_id: str) -> str:
    """Get installation access token with caching."""
    # Check cache first
    cached_token = await cache.get(f"github_token_{installation_id}")
    if cached_token:
        return cached_token

    # Generate new token
    jwt_token = generate_jwt_token(app_id, private_key)
    headers = {
        'Authorization': f'Bearer {jwt_token}',
        'Accept': 'application/vnd.github.v3+json'
    }

    response = await http_client.post(
        f'https://api.github.com/app/installations/{installation_id}/access_tokens',
        headers=headers
    )

    token_data = response.json()
    token = token_data['token']
    expires_at = token_data['expires_at']

    # Cache token with expiration
    ttl = calculate_ttl(expires_at)
    await cache.set(f"github_token_{installation_id}", token, ttl=ttl)

    return token
```

## Service Implementation

### GitHub API Client

#### Base GitHub Service
```python
class GitHubService:
    def __init__(self, app_id: str, private_key: str, installation_id: str):
        self.app_id = app_id
        self.private_key = private_key
        self.installation_id = installation_id
        self.base_url = "https://api.github.com"

    async def _get_headers(self) -> dict:
        """Get authenticated headers for API requests."""
        token = await self.get_installation_token()
        return {
            'Authorization': f'token {token}',
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'TaskMaster-GitHub-Integration'
        }

    async def _make_request(self, method: str, endpoint: str, **kwargs) -> dict:
        """Make authenticated request to GitHub API."""
        headers = await self._get_headers()
        url = f"{self.base_url}{endpoint}"

        async with httpx.AsyncClient() as client:
            response = await client.request(
                method, url, headers=headers, **kwargs
            )

        if response.status_code == 403 and 'rate limit' in response.text.lower():
            raise RateLimitExceeded(response.headers.get('X-RateLimit-Reset'))

        response.raise_for_status()
        return response.json()
```

### Repository Management Service

#### GitHubRepositoryService
```python
class GitHubRepositoryService(GitHubService):
    async def list_repositories(
        self,
        org: str,
        page: int = 1,
        per_page: int = 30,
        search: str = None,
        visibility: str = "all"
    ) -> dict:
        """List organization repositories."""
        params = {
            'page': page,
            'per_page': per_page,
            'type': visibility
        }

        if search:
            # Use search API for filtering
            params['q'] = f"{search} org:{org}"
            endpoint = "/search/repositories"
        else:
            endpoint = f"/orgs/{org}/repos"

        return await self._make_request('GET', endpoint, params=params)

    async def get_repository_teams(self, owner: str, repo: str) -> dict:
        """Get teams with access to repository."""
        endpoint = f"/repos/{owner}/{repo}/teams"
        return await self._make_request('GET', endpoint)

    async def add_team_to_repository(
        self,
        owner: str,
        repo: str,
        team_slug: str,
        permission: str
    ) -> dict:
        """Add team to repository with specific permission.

        Uses GitHub's official API endpoint:
        PUT /orgs/{org}/teams/{team_slug}/repos/{owner}/{repo}

        Permission values: pull, push, maintain, admin
        """
        endpoint = f"/orgs/{owner}/teams/{team_slug}/repos/{owner}/{repo}"
        data = {'permission': permission}
        return await self._make_request('PUT', endpoint, json=data)

    async def remove_team_from_repository(
        self,
        owner: str,
        repo: str,
        team_slug: str
    ) -> dict:
        """Remove team from repository.

        Uses GitHub's official API endpoint:
        DELETE /orgs/{org}/teams/{team_slug}/repos/{owner}/{repo}
        """
        endpoint = f"/orgs/{owner}/teams/{team_slug}/repos/{owner}/{repo}"
        return await self._make_request('DELETE', endpoint)

    async def bulk_assign_teams_to_repositories(
        self,
        org: str,
        repository_names: list,
        team_assignments: list,
        replace_existing: bool = False
    ) -> dict:
        """Bulk assign teams to multiple repositories.

        Uses GitHub's official API endpoint for each assignment:
        PUT /orgs/{org}/teams/{team_slug}/repos/{owner}/{repo}
        """
        results = []

        for repo_name in repository_names:
            if replace_existing:
                # Remove existing teams first
                current_teams = await self.get_repository_teams(org, repo_name)
                for team in current_teams:
                    await self.remove_team_from_repository(
                        org, repo_name, team['slug']
                    )

            # Add new team assignments
            for assignment in team_assignments:
                try:
                    await self.add_team_to_repository(
                        org, repo_name, assignment['team_slug'], assignment['permission']
                    )
                    results.append({
                        'repository_name': repo_name,
                        'team_slug': assignment['team_slug'],
                        'status': 'success',
                        'permission': assignment['permission']
                    })
                except Exception as e:
                    results.append({
                        'repository_name': repo_name,
                        'team_slug': assignment['team_slug'],
                        'status': 'failed',
                        'error': str(e)
                    })

        return {
            'assignments_created': len([r for r in results if r['status'] == 'success']),
            'assignments_failed': len([r for r in results if r['status'] == 'failed']),
            'results': results
        }
```

### User Management Service

#### GitHubUserService
```python
class GitHubUserService(GitHubService):
    async def list_organization_members(
        self,
        org: str,
        page: int = 1,
        per_page: int = 30,
        role: str = "all"
    ) -> dict:
        """List organization members."""
        params = {
            'page': page,
            'per_page': per_page,
            'role': role
        }
        endpoint = f"/orgs/{org}/members"
        return await self._make_request('GET', endpoint, params=params)

    async def get_user_teams(self, org: str, username: str) -> dict:
        """Get user's team memberships in organization."""
        endpoint = f"/orgs/{org}/teams"
        all_teams = await self._make_request('GET', endpoint)

        user_teams = []
        for team in all_teams:
            try:
                # Check if user is member of this team
                member_endpoint = f"/orgs/{org}/teams/{team['slug']}/memberships/{username}"
                membership = await self._make_request('GET', member_endpoint)
                user_teams.append({
                    **team,
                    'role': membership['role']
                })
            except httpx.HTTPStatusError as e:
                if e.response.status_code != 404:
                    raise

        return user_teams

    async def bulk_assign_users_to_teams(
        self,
        org: str,
        user_ids: list,
        team_assignments: list,
        replace_existing: bool = False
    ) -> dict:
        """Bulk assign users to multiple teams."""
        results = []

        for user_id in user_ids:
            user_info = await self.get_user(user_id)
            username = user_info['login']

            if replace_existing:
                # Remove from existing teams first
                current_teams = await self.get_user_teams(org, username)
                for team in current_teams:
                    await self.remove_user_from_team(org, team['slug'], username)

            # Add to new teams
            for assignment in team_assignments:
                try:
                    team_info = await self.get_team(assignment['team_id'])
                    await self.add_user_to_team(
                        org, team_info['slug'], username, assignment['role']
                    )
                    results.append({
                        'user_id': user_id,
                        'team_id': assignment['team_id'],
                        'status': 'success',
                        'role': assignment['role']
                    })
                except Exception as e:
                    results.append({
                        'user_id': user_id,
                        'team_id': assignment['team_id'],
                        'status': 'failed',
                        'error': str(e)
                    })

        return {
            'assignments_created': len([r for r in results if r['status'] == 'success']),
            'assignments_failed': len([r for r in results if r['status'] == 'failed']),
            'results': results
        }
```

### Team Management Service

#### GitHubTeamService
```python
class GitHubTeamService(GitHubService):
    async def list_teams(self, org: str, page: int = 1, per_page: int = 30) -> dict:
        """List organization teams."""
        params = {'page': page, 'per_page': per_page}
        endpoint = f"/orgs/{org}/teams"
        return await self._make_request('GET', endpoint, params=params)

    async def get_team_members(self, org: str, team_slug: str) -> dict:
        """Get team members."""
        endpoint = f"/orgs/{org}/teams/{team_slug}/members"
        return await self._make_request('GET', endpoint)

    async def get_team_repositories(self, org: str, team_slug: str) -> dict:
        """Get repositories accessible to team."""
        endpoint = f"/orgs/{org}/teams/{team_slug}/repos"
        return await self._make_request('GET', endpoint)

    async def add_user_to_team(
        self,
        org: str,
        team_slug: str,
        username: str,
        role: str = "member"
    ) -> dict:
        """Add user to team with specific role."""
        endpoint = f"/orgs/{org}/teams/{team_slug}/memberships/{username}"
        data = {'role': role}
        return await self._make_request('PUT', endpoint, json=data)

    async def remove_user_from_team(
        self,
        org: str,
        team_slug: str,
        username: str
    ) -> dict:
        """Remove user from team."""
        endpoint = f"/orgs/{org}/teams/{team_slug}/memberships/{username}"
        return await self._make_request('DELETE', endpoint)
```

## Error Handling and Rate Limiting

### Rate Limit Management
```python
class RateLimitManager:
    def __init__(self):
        self.rate_limit_remaining = 5000
        self.rate_limit_reset = None

    async def check_rate_limit(self, response_headers: dict):
        """Update rate limit information from response headers."""
        self.rate_limit_remaining = int(
            response_headers.get('X-RateLimit-Remaining', 0)
        )
        self.rate_limit_reset = int(
            response_headers.get('X-RateLimit-Reset', 0)
        )

        if self.rate_limit_remaining < 100:
            # Log warning when approaching rate limit
            logger.warning(
                f"GitHub API rate limit low: {self.rate_limit_remaining} remaining"
            )

    async def wait_for_rate_limit_reset(self):
        """Wait for rate limit to reset if necessary."""
        if self.rate_limit_remaining <= 0 and self.rate_limit_reset:
            wait_time = self.rate_limit_reset - time.time()
            if wait_time > 0:
                logger.info(f"Waiting {wait_time} seconds for rate limit reset")
                await asyncio.sleep(wait_time)
```

### Error Handling
```python
class GitHubAPIError(Exception):
    """Base exception for GitHub API errors."""
    pass

class RateLimitExceeded(GitHubAPIError):
    """Raised when GitHub API rate limit is exceeded."""
    def __init__(self, reset_time: str):
        self.reset_time = reset_time
        super().__init__(f"Rate limit exceeded. Resets at {reset_time}")

class InsufficientPermissions(GitHubAPIError):
    """Raised when GitHub App lacks required permissions."""
    pass

class ResourceNotFound(GitHubAPIError):
    """Raised when requested resource is not found."""
    pass
```

## Configuration

### Environment Variables
```bash
# GitHub App Configuration
GITHUB_APP_ID=123456
GITHUB_APP_PRIVATE_KEY_PATH=/path/to/private-key.pem
GITHUB_APP_INSTALLATION_ID=12345678

# Organization Configuration
GITHUB_ORG_NAME=your-organization

# API Configuration
GITHUB_API_BASE_URL=https://api.github.com
GITHUB_API_TIMEOUT=30
GITHUB_API_RETRY_ATTEMPTS=3
```

### Service Configuration
```python
github_config = {
    'app_id': os.getenv('GITHUB_APP_ID'),
    'private_key_path': os.getenv('GITHUB_APP_PRIVATE_KEY_PATH'),
    'installation_id': os.getenv('GITHUB_APP_INSTALLATION_ID'),
    'organization': os.getenv('GITHUB_ORG_NAME'),
    'api_timeout': int(os.getenv('GITHUB_API_TIMEOUT', 30)),
    'retry_attempts': int(os.getenv('GITHUB_API_RETRY_ATTEMPTS', 3))
}
```
