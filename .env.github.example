# GitHub Integration Configuration
# Copy this file to .env and configure the values for your setup

# =============================================================================
# GitHub Authentication Method
# =============================================================================
# Choose authentication method: 'github_app' or 'personal_access_token'
GITHUB__AUTH_METHOD=github_app

# =============================================================================
# GitHub App Configuration (Option 1 - Recommended for Production)
# =============================================================================
# GitHub App ID (found in your GitHub App settings)
GITHUB__APP_ID=

# GitHub App Installation ID (found in your organization's installed apps)
GITHUB__INSTALLATION_ID=

# GitHub App Private Key (choose one of the following options):
# Option A: Path to private key file
GITHUB__PRIVATE_KEY_PATH=/path/to/your/private-key.pem

# Option B: Private key content as environment variable (alternative to file path)
# GITHUB__PRIVATE_KEY="-----BEGIN RSA PRIVATE KEY-----\n...\n-----END RSA PRIVATE KEY-----"

# =============================================================================
# Personal Access Token Configuration (Option 2 - Good for Development)
# =============================================================================
# Personal Access Token (classic) with required scopes:
# - repo (for private repositories) or public_repo (for public repositories only)
# - read:org (to read organization information)
# - admin:org (to manage teams and memberships)
GITHUB__PERSONAL_ACCESS_TOKEN=

# =============================================================================
# Organization Configuration
# =============================================================================
# GitHub organization name
GITHUB__ORG_NAME=your-organization-name

# =============================================================================
# API Configuration (Optional - defaults provided)
# =============================================================================
# GitHub API base URL (default: https://api.github.com)
# GITHUB__API_BASE_URL=https://api.github.com

# API timeout in seconds (default: 30)
# GITHUB__API_TIMEOUT=30

# API retry attempts (default: 3)
# GITHUB__API_RETRY_ATTEMPTS=3

# =============================================================================
# Setup Instructions
# =============================================================================

# For GitHub App Authentication:
# 1. Create a GitHub App in your organization settings
# 2. Set required permissions:
#    - Repository permissions: Administration (Read & Write), Metadata (Read)
#    - Organization permissions: Members (Read & Write), Administration (Read)
# 3. Install the app in your organization
# 4. Generate and download the private key
# 5. Set the environment variables above

# For Personal Access Token Authentication:
# 1. Go to GitHub Settings > Developer settings > Personal access tokens
# 2. Generate a new token (classic) with required scopes:
#    - repo (for private repos) or public_repo (for public repos only)
#    - read:org (to read organization info)
#    - admin:org (to manage teams and memberships)
# 3. Set the GITHUB__PERSONAL_ACCESS_TOKEN variable above

# Required Scopes for Personal Access Token:
# - repo: Full control of private repositories
# - public_repo: Access public repositories (alternative to repo for public-only)
# - read:org: Read org and team membership, read org projects
# - admin:org: Fully manage the organization and its teams, projects, and memberships

# Testing the Configuration:
# After setting up the environment variables, you can test the connection using:
# GET /v1/github/test-connection
